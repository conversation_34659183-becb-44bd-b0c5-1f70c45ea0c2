using System;
using System.Collections.Generic;
using UnityEngine;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public enum TruckState
    {
        Lock,    // 未解锁
        Idle,    // 空闲
        Depart,  // 出发
        Reward   // 到站奖励
    }

    public class UITruckItem : MonoBehaviour
    {
        public RectTransform rectTransform;
        public UIButton button;
        public GameObject empty;
        public GameObject imgLock;
        public GameObject imgAdd;

        public GameObject truck;
        public UIImage icon;
        public UIButton btnBubble;
        public GameObject bubble;
        public UIImage box;
        public SkinnedMeshRenderer meshRenderer;
        public List<Material> materials;

        public int index;
        public TruckState truckState;
        public bool isOther;
        public int quality;
        public Action<UITruckItem> callback;
        public Trade.TradeCargoTransport truckData;

        void Update()
        {
            if (truckData == null) return;
            if (isOther) return;

            // 运输中
            if (truckData.ArrivalTime > 0)
            {
                truckState = TruckState.Depart;

                // 已到站
                long remainTime = truckData.ArrivalTime - (long)TimeComponent.Now;
                if (remainTime <= 0)
                {
                    truckState = TruckState.Reward;
                    Refresh();
                }
            }
        }

        public void Init()
        {
            button.onClick.AddListener(OnClickButton);
            btnBubble.onClick.AddListener(OnClickBtnBubble);
        }

        public void Refresh()
        {
            icon.SetImage($"Sprite/ui_maoyi/maoyi_car_{quality}.png", false);
            if (0 <= quality - 1 && quality - 1 < materials.Count)
            {
                meshRenderer.material = materials[quality - 1];
            }

            if (isOther)
            {
                empty.SetActive(false);
                truck.SetActive(true);
                bubble.SetActive(false);
            }
            else
            {
                switch (truckState)
                {
                    case TruckState.Lock:
                    default:
                        empty.SetActive(true);
                        imgLock.SetActive(true);
                        imgAdd.SetActive(false);
                        truck.SetActive(false);
                        bubble.SetActive(false);
                        break;
                    case TruckState.Idle:
                        empty.SetActive(true);
                        imgLock.SetActive(false);
                        imgAdd.SetActive(true);
                        truck.SetActive(false);
                        bubble.SetActive(false);
                        break;
                    case TruckState.Depart:
                        empty.SetActive(false);
                        truck.SetActive(true);
                        bubble.SetActive(false);
                        break;
                    case TruckState.Reward:
                        empty.SetActive(false);
                        truck.SetActive(true);
                        bubble.SetActive(true);
                        break;
                }
            }
        }

        void OnClickButton()
        {
            callback?.Invoke(this);
            if (isOther)
            {

            }
            else
            {
                switch (truckState)
                {
                    case TruckState.Lock:
                    default:
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(1100455)
                        });
                        break;
                    case TruckState.Idle:
                        int count = GameEntry.TradeTruckData.TruckTradeTodayCount;
                        int countMax = 4;
                        if (count >= countMax)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = "发车次数已耗尽"
                            });
                            return;
                        }
                        ColorLog.Pink("货车车位信息", index);
                        Trade.TradeCargoTransport resp = GameEntry.TradeTruckData.GetTruckByPos(index);
                        if (resp != null)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDepartForm, resp);
                            return;
                        }
                        GameEntry.TradeTruckData.RequestTruckParking(index, (result) =>
                        {
                            ColorLog.Pink("请求货车车位信息", result);
                            if (result != null)
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDepartForm, result.Van);
                            }
                        });
                        break;
                    case TruckState.Depart:
                        break;
                    case TruckState.Reward:
                        GetReward();
                        break;
                }
            }
        }

        void OnClickBtnBubble()
        {
            GetReward();
        }

        void GetReward()
        {
            if (truckData == null) return;
            long remainTime = truckData.ArrivalTime - (long)TimeComponent.Now;
            if (remainTime <= 0)
            {
                ColorLog.Pink("货车数据", truckData);
                GameEntry.TradeTruckData.RequestTruckReward(truckData.Id, (rewardResult) =>
                {
                    ColorLog.Pink("货车领取奖励", rewardResult);

                    // 刷新货车状态
                    truckState = TruckState.Idle;
                    truckData.ArrivalTime = 0;

                    // 刷新货车列表
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckForm);

                    // 打开货车详情界面
                    GameEntry.TradeTruckData.RequestTruckDetail(truckData.Id, (result) =>
                    {
                        ColorLog.Pink("货车详情", result);
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDetailForm, new UITradeTruckDetailFormParams()
                        {
                            type = TruckDetailType.Reward,
                            truckDetail = result
                        });

                        // 打开领奖界面
                        List<reward> rewards = new();
                        foreach (var item in rewardResult.Article)
                        {
                            rewards.Add(new reward()
                            {
                                item_id = (itemid)item.Code,
                                num = item.Amount
                            });
                        }
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                    });
                });
            }
        }
    }
}
