using GameFramework;
using GameFramework.Event;
using Team;

namespace Game.Hotfix
{
    public class TeamChangeEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(TeamChangeEventArgs).GetHashCode();

        public override int Id
        {
            get { return EventId; }
        }

        public TeamType TeamType { get; private set; }
        
        public static TeamChangeEventArgs Create(TeamType teamType)
        {
            TeamChangeEventArgs teamChangeEventArgs = ReferencePool.Acquire<TeamChangeEventArgs>();
            teamChangeEventArgs.TeamType = teamType;
            return teamChangeEventArgs;
        }
        
        public override void Clear()
        {
            
        }
    }
}