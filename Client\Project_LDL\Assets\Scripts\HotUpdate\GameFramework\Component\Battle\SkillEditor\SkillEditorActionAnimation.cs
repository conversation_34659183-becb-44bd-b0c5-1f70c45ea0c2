#if UNITY_EDITOR
using System;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    public class SkillEditorActionAnimation : SkillEditorActionBase
    {
        [PropertyRange(0, "Max")]
        [LabelText("动作开始时间")]
        public float StartTime;
        
        [LabelText("动画名称")] public skill_act_group AnimationName;

        public SkillEditorActionAnimation(SkillEditorPreview skillEditorPreview) : base(skillEditorPreview)
        {
        }

        public override void WriteData(skill_show data)
        {
            base.WriteData(data);
            data.a_start_time = StartTime;
            data.a_name = AnimationName;
        }

        public override void ReadData(skill_show data)
        {
            base.ReadData(data);
            StartTime = data.a_start_time;
            AnimationName = data.a_name;
        }
    }
}
#endif