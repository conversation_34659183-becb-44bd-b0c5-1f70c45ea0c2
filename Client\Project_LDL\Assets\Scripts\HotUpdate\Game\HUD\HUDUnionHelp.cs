using UnityEngine;

namespace Game.Hotfix
{
    public class HUDUnionHelp : HUDItemTickAble
    {
        [SerializeField] private UIButton m_btnDisplayRoot;

        private EL_Building m_Building;
        private BuildingModule m_BuildingModule;

        void Start()
        {
            m_btnDisplayRoot.onClick.AddListener(OnBtnClick);
        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Building = Owner as EL_Building;
            m_BuildingModule = m_Building?.GetBuildingModule();

            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnUnionHelpChange, OnUnionHelpChange);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnUnionHelpChange, OnUnionHelpChange);
        }

        protected override Vector3 GetOffset()
        {
            if (m_BuildingModule != null)
            {
                var offset = m_BuildingModule.GetOffsetCenter();
                Vector3 vec;
                if (m_BuildingModule.GetBuildingType() == Config.buildtype.buildtype_researchcenter)
                {
                    vec = new Vector3(offset.x, 4f, offset.y);
                }
                else
                {
                    vec = new Vector3(offset.x, 2f, offset.y);
                }
                return vec;
            }
            return base.GetOffset();
        }

        private void OnUnionHelpChange(object obj)
        {
            OnCheckClose();
        }

        private void OnBtnClick()
        {
            if (OnCheckClose())
            {
                QueueModule queueModule = null;
                var queueType = m_BuildingModule.m_unionHelpList[0];
                var buildingId = (uint)m_BuildingModule.BuildingId;
                if (queueType == Build.QueueType.BuildUpgrade)
                    queueModule = GameEntry.LogicData.QueueData.GetBuildQueueModule(buildingId);
                else if (queueType == Build.QueueType.BuildSoldierTreatment)
                    queueModule = GameEntry.LogicData.QueueData.GetTreatQueueModule(buildingId);
                else if (queueType == Build.QueueType.BuildTech)
                    queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule(buildingId);
                    
                if (queueModule != null)
                {
                    GameEntry.LogicData.BuildingData.BuildQueueHelpReq(queueModule.BindBuildNo, queueModule.QueueUid, (resp) =>
                    {
                        m_BuildingModule.SetUnionHelp(queueType, false);
                        OnCheckClose();

                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(80400010)
                        });
                    });
                }
                else
                {
                    m_BuildingModule.SetUnionHelp(queueType, false);
                    OnBtnClick();
                }
            }
        }

        private bool OnCheckClose()
        {
            var helpList = m_BuildingModule.m_unionHelpList;
            if (helpList == null || helpList.Count <= 0)
            {
                GameEntry.HUD.HideHUD(this);
                return false;
            }
            return true;
        }
    }
}