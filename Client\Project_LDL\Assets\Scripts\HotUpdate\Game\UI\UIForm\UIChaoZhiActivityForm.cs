using System;
using System.Collections.Generic;
using Activity;
using Game.Hotfix.Config;
using GameFramework.Event;

namespace Game.Hotfix
{
    public partial class UIChaoZhiActivityForm : UGuiFormEx
    {
        private UISwitchPage switchPage;
        private Dictionary<int, string> prefabList;
        
        private List<ActivityTime> activityList;
        private ChaoZhiData Manager;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            Manager = GameEntry.LogicData.ChaoZhiData;
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            var targetIndex = 0;
            
            if (prefabList == null)
            {
                prefabList = new Dictionary<int, string>();
            }
            else
            {
                prefabList.Clear();
            }
            
            var list = new List<ValueTuple<int,string, string>>();//唯一id，名称，预制体
            activityList = Manager.GetActivityList();
            
            //先锋战令特殊处理
            var specialList = new List<int>
            {
                (int)Config.activity_template.activity_template_100115,
                (int)Config.activity_template.activity_template_400101,
            };
            var useList = new List<ActivityTime>();
            foreach (var activity in activityList)
            {
                var uniqueId = (int)activity.Template;
                if(specialList.Contains(uniqueId))continue;
                var config = Manager.GetActivityConfig(uniqueId);
                if (config != null)
                {
                    var prefab = Manager.GetTemplatePrefab(uniqueId);
                    list.Add((uniqueId,ToolScriptExtend.GetLang(config.name),prefab));
                    useList.Add(activity);
                }
            }
            
            switchPage = m_goSwitchPage.GetComponent<UISwitchPage>();
            switchPage.EnumUIForm = EnumUIForm.UIChaoZhiActivityForm;
            for (var i = 0; i < useList.Count; i++)
            {
                switchPage.BindDataByIndex(i,useList[i]);
            }
            switchPage.OnInit(m_goTagItem, m_goTagRoot.transform, m_goPanelRoot.transform, list, OnSelectLogic);
            switchPage.SwitchTagGroup.ScrollToFirst();
           
            switchPage.SelectPageByIndex(targetIndex);
            switchPage.UseTimer = true;
            
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            GameEntry.Event.Subscribe(RedPointEventArgs.EventId, OnRedDotChange);
            
            //初始化页签红点
            foreach (var activity in useList)
            {
                var id = (int)activity.Template;
                var count = Manager.GetRedDotCountById(id);
                switchPage.SwitchTagGroup.CheckNumDotLogic(id,count);
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            prefabList.Clear();
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            GameEntry.Event.Unsubscribe(RedPointEventArgs.EventId, OnRedDotChange);
        }
        
        //红点事件响应
        void OnRedDotChange(object sender, GameEventArgs e)
        {
            var uniqueId = switchPage.GetCurUniqueId();
            CheckRedDot(uniqueId);
        }

        private void CheckRedDot(int uniqueId)
        {
            var type = (paymenttype)uniqueId;
            var EnumRedStr = "";
            switch (type)
            {
                case paymenttype.paymenttype_nil:
                case paymenttype.paymenttype_dailydeal:
                    EnumRedStr = EnumRed.Mall_DialyDeal.ToString();
                    break;
                case paymenttype.paymenttype_dailymusthave:
                    EnumRedStr = EnumRed.Mall_DailyBuy.ToString();
                    break;
                case paymenttype.paymenttype_monthycard:
                    EnumRedStr = EnumRed.Mall_MonthlyCard.ToString();
                    break;
                case paymenttype.paymenttype_weeklycard:
                    EnumRedStr = EnumRed.Mall_WeeklyCard.ToString();
                    break;
                case paymenttype.paymenttype_dawnfund:
                    EnumRedStr = EnumRed.Mall_GrowthFund.ToString();
                    break;
                default:
                    break;
            }
            
            if (string.IsNullOrEmpty(EnumRedStr))
            {
                return;
            }
            var count = RedPointManager.Instance.GetRedCount(EnumRedStr);

            if (uniqueId == 0)
            {
                uniqueId = (int)paymenttype.paymenttype_dailydeal;
            }
            switchPage.SwitchTagGroup.CheckNumDotLogic(uniqueId,count);
            
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            switchPage.OnRefresh(userData);
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "RedDot")
            {
                var count = Manager.GetRedDotCountById(param);
                switchPage.SwitchTagGroup.CheckNumDotLogic(param,count);
            }
        }

        private void OnBtnExitClick()
        {
             Close();
        }
        
        private void OnSelectLogic(int index)
        {
            
            
        }
        
        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            switchPage.OnRefresh(("ChaoZhi_RoleUpStar",1));
        }
        
        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);
            
            // SetParticleSystemSortingOrder(m_goMakeEffect, Depth);
        }
    }
}
