using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using Mosframe;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIHeroForm : UGuiFormEx
    {
        public int SelectType => selectType;
        private int selectType = -1;
        private List<Transform> itemList = new();
        private bool isSetDepth = false;
        private bool isReset = false;

        readonly Dictionary<int, string> recommendIconDic = new()
        {
            { 0, "Sprite/ui_hero/heroliebiao_icon_zhuangbeituijian_2.png" },
            { 1, "Sprite/ui_hero/heroliebiao_icon_zhuangbeituijian_1.png" },
        };

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            // OnTest();

            int childCount = m_transBtnList.childCount;
            var heroData = GameEntry.LogicData.HeroData;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);

                if (i > 0)
                {
                    var icon = trans.Find("bg/icon").GetComponent<Image>();
                    var selecSp = trans.Find("selectBg/selecSp").GetComponent<Image>();
                    icon.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                    selecSp.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                }

                int index = i;
                var rectTrans = trans.GetComponent<RectTransform>();
                rectTrans.sizeDelta = new Vector2(248, 91);

                var btn = trans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    OnSelectBtn(index);
                });
            }

            InitDropdown();
            m_goRecommond.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
            m_goDropdownOption.SetActive(false);
            m_btnMaskDropdown.gameObject.SetActive(false);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);

            if (isSetDepth)
                OnInitForm();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            GameEntry.LogicData.HeroData.ClearHeroNew();
            isReset = false;
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            var effectObj = m_transHeroItem.Find("starImg").gameObject;
            SetParticleSystemSortingOrder(effectObj, Depth);

            var rect = m_scrollview.GetComponent<RectTransform>();
            ToolScriptExtend.SetParticleSystemMaskSize(effectObj, rect);

            if (!isSetDepth)
            {
                isSetDepth = true;
                OnInitForm();
            }
        }

        protected override void OnCover()
        {
            base.OnReveal();
            isReset = true;
        }

        protected override void OnReveal()
        {
            base.OnReveal();
            if (isReset)
            {
                isReset = false;
                OnUpdateInfo();
            }
        }


        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnRecruitClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIRecruitForm);
        }

        private void OnBtnEquipBetterClick()
        {
            // 显示装备推荐弹窗
            m_goRecommond.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnMaskClick()
        {
            // 隐藏装备推荐弹窗
            m_goRecommond.SetActive(false);
            m_btnMask.gameObject.SetActive(false);

            m_goDropdownOption.SetActive(false);
            m_btnMaskDropdown.gameObject.SetActive(false);
        }

        private void OnBtnSwitchClick()
        {
            // 切换开关状态
            bool isOn = m_goSwitchOn.activeSelf;

            if (isOn)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                {
                    Title = ToolScriptExtend.GetLang(1100147),
                    Content = ToolScriptExtend.GetLang(1344),
                    ConfirmText = ToolScriptExtend.GetLang(1100144),
                    CancelText = ToolScriptExtend.GetLang(1100143),
                    OnClickConfirm = (a) => { SwitchEquipmentRecommond(isOn); },
                });
            }
            else
            {
                SwitchEquipmentRecommond(isOn);
            }
        }

        private void OnBtnMaskDropdownClick()
        {
            m_goDropdownOption.SetActive(false);
            m_btnMaskDropdown.gameObject.SetActive(false);
        }

        private void OnBtnDropdownClick()
        {
            // 显示/隐藏下拉选项
            bool isShow = !m_goDropdownOption.activeSelf;
            m_goDropdownOption.SetActive(isShow);
            m_btnMaskDropdown.gameObject.SetActive(isShow);
        }

        /// <summary>
        /// 初始化下拉框
        /// </summary>
        void InitDropdown()
        {
            foreach (Transform item in m_transDropdownOption)
            {
                UIToggle toggle = item.GetComponent<UIToggle>();
                toggle.onValueChanged.AddListener((isOn) =>
                {
                    m_goDropdownOption.SetActive(false);
                    m_btnMaskDropdown.gameObject.SetActive(false);

                    UIText label = item.GetChild(2).GetComponent<UIText>();
                    string colorStr = isOn ? "#000000" : "#7a7780";
                    ColorUtility.TryParseHtmlString(colorStr, out Color color);
                    label.color = color;

                    if (isOn)
                    {
                        m_txtDropdown.text = label.text;
                        GameEntry.EquipmentData.CurRecommendTeamIndex = toggle.toggleType;
                    }
                });

                if (GameEntry.EquipmentData.CurRecommendTeamIndex == toggle.toggleType)
                {
                    toggle.isOn = true;
                    UIText label = item.GetChild(2).GetComponent<UIText>();
                    m_txtDropdown.text = label.text;
                }
            }
        }

        /// <summary>
        /// 切换装备推荐状态
        /// </summary>
        /// <param name="isOn">是否开启</param>
        void SwitchEquipmentRecommond(bool isOn)
        {
            m_goSwitchOn.SetActive(!isOn);
            m_goSwitchOff.SetActive(isOn);

            UIImage image = m_btnEquipBetter.GetComponent<UIImage>();
            string path = isOn ? recommendIconDic[0] : recommendIconDic[1];
            image.SetImage(path, false);
        }

        private void OnInitForm()
        {
            if (selectType != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }
        }

        private void OnTest()
        {
            GameObject obj = transform.Find("test/item").gameObject;

            // m_TableViewH.GetItemCount = () => { return 12; };
            // m_TableViewH.GetItemGo = () => { return obj; };
            // m_TableViewH.UpdateItemCell = (int i, GameObject go) => { };
            // m_TableViewH.InitTableViewByIndex(0);

            // m_TableViewV.GetItemCount = () => { return 12; };
            // m_TableViewV.GetItemGo = () => { return obj; };
            // m_TableViewV.UpdateItemCell = (int i, GameObject go) => { };
            // m_TableViewV.InitTableViewByIndex(0);

            // m_TableViewD.GetCellCount = (tableView) => { return 12; };
            // m_TableViewD.GetCellSize = (tableView, index) => { return new Vector2(200, 200); };
            // m_TableViewD.UpdateCell = (tableView, index) =>
            // {
            //     if (tableView == null)
            //         return new Mosframe.CCTableViewCell();

            //     Mosframe.CCTableViewCell cell = tableView.GetReusableCell();
            //     if (cell == null)
            //     {
            //         GameObject go = GameObject.Instantiate(obj);
            //         cell = go.AddComponent<Mosframe.CCTableViewCell>();
            //         go.SetActive(true);
            //     }
            //     return cell;
            // };
            // m_TableViewD.ReloadData(true);
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnSelectBtn(int index)
        {
            if (selectType == index)
                return;

            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                rectTrans.sizeDelta = new Vector2(274, 91);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            if (selectType > -1)
            {
                trans = m_transBtnList.GetChild(selectType);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    rectTrans.sizeDelta = new Vector2(248, 91);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }
            selectType = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            List<HeroModule> heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true);
            int count = heroList.Count;
            int num = itemList.Count;
            int len = count > num ? count : num;
            Transform rootTrans = m_scrollview.content;
            for (int i = 0; i < len; i++)
            {
                Transform item;
                if (itemList.Count < i + 1)
                {
                    item = GameObject.Instantiate(m_transHeroItem, rootTrans);
                    itemList.Add(item);
                }
                else
                {
                    item = itemList[i];
                }
                bool isShow = i < count;
                if (isShow)
                {
                    OnUpdateItem(item, heroList, i);
                }
                item.gameObject.SetActive(isShow);
            }
        }

        private void OnUpdateItem(Transform item, List<HeroModule> heroList, int index)
        {
            var btn = item.GetComponent<Button>();
            var heroBg = item.Find("heroBg").GetComponent<UIImage>();
            var heroSp = item.Find("heroMask/heroSp").GetComponent<UIImage>();
            var positionSp = item.Find("positionSp").GetComponent<UIImage>();
            var teamBg = item.Find("teamBg").gameObject;
            var teamTxt = item.Find("teamBg/teamTxt").GetComponent<UIText>();
            var servicesSp = item.Find("servicesSp").GetComponent<UIImage>();
            var levelTxt = item.Find("levelTxt").GetComponent<UIText>();
            var starBg = item.Find("starBg").GetComponent<UIImage>();
            var starObj = item.Find("starBg/starObj");
            var chipTxt = item.Find("starBg/chipTxt").GetComponent<UIText>();
            var maskBg = item.Find("maskBg").gameObject;
            var redImg = item.Find("redImg").gameObject;
            var starImg = item.Find("starImg").gameObject;
            var newImg = item.Find("newImg").gameObject;

            var heroData = GameEntry.LogicData.HeroData;
            var heroVo = heroList[index];

            heroBg.SetImage(GetItemBgPath(heroVo.Quality));
            heroSp.SetImage(heroVo.HeroHead);
            positionSp.SetImage(heroData.GetPositionImgPath(heroVo.Position), true);
            servicesSp.SetImage(heroData.GetServicesImgPath(heroVo.Services), true);
            starBg.SetImage(GetStarBgPath(heroVo.Quality));

            teamTxt.text = heroVo.TeamId + "";
            teamBg.SetActive(heroVo.TeamId > 0);

            var isActive = heroVo.IsActive;
            if (isActive)
            {
                var starNum = heroVo.StarNum;
                var starOrder = heroVo.StarOrder;
                var count = starObj.childCount;
                for (int i = 0; i < count; i++)
                {
                    var starSp = starObj.GetChild(i).GetComponent<UIImage>();
                    string pathStr;
                    if (i < starNum) { pathStr = "Sprite/ui_hero/hero_icon_star5.png"; }
                    else if (i < starNum + 1 && starOrder > 0) { pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder); }
                    else { pathStr = "Sprite/ui_hero/hero_icon_star0.png"; }
                    starSp.SetImage(pathStr);
                }
                levelTxt.text = string.Format("Lv.{0}", heroVo.level);
                chipTxt.text = "";
            }
            else
            {
                var count = GameEntry.LogicData.BagData.GetAmountById(heroVo.Piece);
                chipTxt.text = string.Format("{0}/{1}", count, heroVo.Combind);
                levelTxt.text = "";
            }
            starObj.gameObject.SetActive(isActive);
            maskBg.SetActive(!isActive && !heroVo.IsCombind);
            redImg.SetActive(heroData.GetSingleHeroRed(heroVo.id));
            starImg.SetActive(heroVo.GetHeroStarRed());

            var newState = heroData.GetHeroNew(heroVo.id);
            newImg.SetActive(newState);

            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() =>
            {
                if (!heroVo.IsActive && heroVo.IsCombind)
                {
                    heroData.OnReqHeroSynthetic(heroVo.id, (resp) =>
                    {
                        heroData.SetHeroNew(heroVo.id, true);
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroNewForm, heroVo.id);
                        ColorLog.Pink(string.Format("英雄合成! id:{0}", resp.Id));
                    });
                    return;
                }
                else if (newState)
                {
                    heroData.SetHeroNew(heroVo.id, false);
                }

                GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroDevelopForm, new UIHeroDevelopFormParams()
                {
                    HeroList = heroList,
                    HeroIndex = index,
                });
            });
        }

        public string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di2_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di2_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di2_blue.png",
            };
        }

        public string GetStarBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroliebiao_kapai_di1_purple.png",
                quality.quality_orange => "Sprite/ui_hero/heroliebiao_kapai_di1_yellow.png",
                _ => "Sprite/ui_hero/heroliebiao_kapai_di1_blue.png",
            };
        }
    }
}
