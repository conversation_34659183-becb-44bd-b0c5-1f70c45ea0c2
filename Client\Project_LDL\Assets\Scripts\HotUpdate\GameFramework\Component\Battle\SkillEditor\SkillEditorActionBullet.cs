#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    
    public class SkillEditorActionBullet:SkillEditorActionBase
    {
        // [ReadOnly][LabelText("开始时间")] public float b_start_time;
        [LabelText("动作时间区间")]
        [MinMaxSlider(0, "Max", true)]
        [OnInspectorInit("@ActionTime.y = Max")]
        public Vector2 ActionTime = new Vector2(0, 1);
        
        [LabelText("目标类型")] public target_type b_target_type;
        [LabelText("复制")] public bool b_is_copy;
        [LabelText("复制间隔")] public float b_copy_interval;
        [LabelText("间隔")] public float b_interval;
        [LabelText("间隔附加Min")] public float b_interval_min;
        [LabelText("间隔附加Max")] public float b_interval_max;
        [LabelText("射击点")] public List<slot> b_fire_slots;
        [LabelText("是否轮射")] public bool b_is_seq;
        [LabelText("受击点挂点")] public slot b_hurt_slots;
        [LabelText("总数量")] public int b_count;
        [LabelText("时间曲线")] public battle_time_ease b_time_line_type;
        [LabelText("时间曲线参数")] public List<int> b_time_line_param;
        [ReadOnly][LabelText("弹道参数")] public string b_trajectory_param;
        [LabelText("枪火特效")] public int b_muzzle_flash_id;
        [LabelText("枪火特效匹配子弹")] public bool b_muzzle_flash_match;
        [LabelText("子弹特效ID")] public int b_effect_id;
        [LabelText("拖尾特效ID")] public int b_trailing_id;
        [LabelText("拖尾消失时间")] public float b_trailing_time;
        [LabelText("被击特效ID")] public int b_hurt_effect_id;
        [LabelText("伤害列表角色是否都有受击特效")] public bool b_hurt_effect_all;
        [LabelText("爆点偏移半径")] public float b_bomb_range;
        [LabelText("受击音效")] public int b_hurt_sound_id;
        [LabelText("爆点偏移半径X")] public float b_bomb_range_x;
        [LabelText("爆点偏移半径Y")] public float b_bomb_range_y;
        
        
        [OnValueChanged("OnBulletTypeChange")]
        [LabelText("弹道类型")] public trajectory b_trajectory;
        

        [HideLabel]
        public BulletEditorInspectorBase BulletEditorInspectorBase;
        
        public SkillEditorActionBullet(SkillEditorPreview skillEditorPreview):base(skillEditorPreview)
        {
            b_fire_slots = new List<slot>();
            b_time_line_param = new List<int>();
        }

        public override void WriteData(skill_show data)
        {
            base.WriteData(data);
            data.b_start_time = ActionTime.x;
            data.b_end_time = ActionTime.y;
            data.b_trajectory = b_trajectory;
            data.b_target_type = b_target_type;
            data.b_is_copy = b_is_copy;
            data.b_copy_interval = b_copy_interval;
            data.b_interval = b_interval;
            data.b_interval_min = b_interval_min;
            data.b_interval_max = b_interval_max;
            data.b_fire_slots = b_fire_slots;
            data.b_is_seq = b_is_seq;
            data.b_hurt_slots = b_hurt_slots;
            data.b_count = b_count;
            data.b_time_line_type = b_time_line_type;
            data.b_time_line_param = b_time_line_param;
            data.b_trajectory_param = b_trajectory_param;
            data.b_muzzle_flash_id = b_muzzle_flash_id;
            data.b_muzzle_flash_match = b_muzzle_flash_match;
            data.b_effect_id = b_effect_id;
            data.b_trailing_id = b_trailing_id;
            data.b_trailing_time = b_trailing_time;
            data.b_hurt_effect_id = b_hurt_effect_id;
            data.b_hurt_effect_all = b_hurt_effect_all;
            data.b_bomb_range = b_bomb_range;
            data.b_hurt_sound_id = b_hurt_sound_id;
            data.b_bomb_range_x = b_bomb_range_x;
            data.b_bomb_range_y = b_bomb_range_y;
            
            if (BulletEditorInspectorBase != null)
                data.b_trajectory_param = JsonUtility.ToJson(BulletEditorInspectorBase);
            else
                data.b_trajectory_param = string.Empty;
        }

        public override void ReadData(skill_show data)
        {
            base.ReadData(data);
            ActionTime.x = data.b_start_time;
            ActionTime.y = data.b_end_time;
            b_trajectory = data.b_trajectory;
            b_target_type = data.b_target_type;
            b_is_copy = data.b_is_copy;
            b_copy_interval = data.b_copy_interval;
            b_interval = data.b_interval;
            b_interval_min = data.b_interval_min;
            b_interval_max = data.b_interval_max;
            b_fire_slots = data.b_fire_slots;
            b_is_seq = data.b_is_seq;
            b_hurt_slots = data.b_hurt_slots;
            b_count = data.b_count;
            b_time_line_type = data.b_time_line_type;
            b_time_line_param = data.b_time_line_param;
            b_trajectory_param = data.b_trajectory_param;
            b_muzzle_flash_id = data.b_muzzle_flash_id;
            b_muzzle_flash_match = data.b_muzzle_flash_match;
            b_effect_id = data.b_effect_id;
            b_trailing_id = data.b_trailing_id;
            b_trailing_time = data.b_trailing_time;
            b_hurt_effect_id = data.b_hurt_effect_id;
            b_hurt_effect_all = data.b_hurt_effect_all;
            b_bomb_range = data.b_bomb_range;
            b_hurt_sound_id = data.b_hurt_sound_id;
            b_bomb_range_x = data.b_bomb_range_x;
            b_bomb_range_y = data.b_bomb_range_y;

            switch (b_trajectory)
            {
                case trajectory.bullet_none:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletNone>(b_trajectory_param);
                    break;
                case trajectory.bullet_normal:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletNormal>(b_trajectory_param);
                    break;
                case trajectory.bullet_parabolic:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletParabolic>(b_trajectory_param);
                    break;
                case trajectory.bullet_laser:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletLaser>(b_trajectory_param);
                    break;
                case trajectory.bullet_bezier:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletBezier>(b_trajectory_param);
                    break;
                case trajectory.bullet_particle:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletParticle>(b_trajectory_param);
                    break;
                case trajectory.bullet_laserlink:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletLaserLink>(b_trajectory_param);
                    break;
                case trajectory.bullet_lightningbolt:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletLightningBolt>(b_trajectory_param);
                    break;
                case trajectory.bullet_bouncing:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletBouncing>(b_trajectory_param);
                    break;
                case trajectory.bullet_lightningboltlink:
                    BulletEditorInspectorBase = JsonUtility.FromJson<EditorBulletLightningBoltLink>(b_trajectory_param);
                    break;
                default:
                {
                    b_trajectory = trajectory.bullet_none;
                    BulletEditorInspectorBase = new EditorBulletNone();
                    break;
                }
            }
        }

        private void OnBulletTypeChange()
        {
            switch (b_trajectory)
            {
                case trajectory.bullet_none:
                    BulletEditorInspectorBase = new EditorBulletNone();
                    break;
                case trajectory.bullet_normal:
                    BulletEditorInspectorBase = new EditorBulletNormal();
                    break;
                case trajectory.bullet_parabolic:
                    BulletEditorInspectorBase = new EditorBulletParabolic();
                    break;
                case trajectory.bullet_laser:
                    BulletEditorInspectorBase = new EditorBulletLaser();
                    break;
                case trajectory.bullet_bezier:
                    BulletEditorInspectorBase = new EditorBulletBezier();
                    break;
                case trajectory.bullet_particle:
                    BulletEditorInspectorBase = new EditorBulletParticle();
                    break;
                case trajectory.bullet_laserlink:
                    BulletEditorInspectorBase = new EditorBulletLaserLink();
                    break;
                case trajectory.bullet_lightningbolt:
                    BulletEditorInspectorBase = new EditorBulletLightningBolt();
                    break;
                case trajectory.bullet_bouncing:
                    BulletEditorInspectorBase = new EditorBulletBouncing();
                    break;
                case trajectory.bullet_lightningboltlink:
                    BulletEditorInspectorBase = new EditorBulletLightningBoltLink();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

    }
}
#endif