using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public partial class UIZhanLingBuyForm : UGuiFormEx
    {
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_goEffect.SetActive(false);
            if (userData == null) return;
            var TemplateId = (int)userData;
            payment paymentData = null;
            if (ToolScriptExtend.GetTable<activity_battlepass_grade>(out var table2))
            {
                var gradeConfig = table2.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
                if (gradeConfig != null)
                {
                    //全购状态  性价比  原价  折扣价  累充积分
                    m_txtDiscount.text = $"{gradeConfig.effectiveness}%";
                    GameEntry.LogicData.MallData.CreateRechargeScore(gradeConfig.payment_id, m_btnBuy.transform,40,-110);
                    if (ToolScriptExtend.GetConfigById<payment>((int)gradeConfig.payment_id, out var data1))
                    {
                        paymentData = data1;
                        m_txtPrice.text = ToolScriptExtend.GetLang(data1.price_lang_id);
                        //购买按钮
                        ToolScriptExtend.BindBtnLogic(m_btnBuy, () =>
                        {
                            GameEntry.PaymentData.Pay(gradeConfig.payment_id);
                            Close();
                        });
                    }
                }
            }
            //----------------主体配置奖励----------------
            var ConfigData = GameEntry.LogicData.ChaoZhiData.GetZLConfig(TemplateId);
            if (ConfigData != null)
            {
                var rewards = new List<reward>();
                var rewardsDic = new Dictionary<itemid, long>();
                foreach (var config in ConfigData.ScoreRewards)
                {
                    if (config.Reward1 != null && config.Reward1.Count > 0)
                    {
                        var source = config.Reward1.ToList();
                        foreach (var node in source)
                        {
                            var key = (itemid)node.ItemId;
                            if (!rewardsDic.ContainsKey(key))
                            {
                                rewardsDic[key] = 0;
                            }
                            rewardsDic[key] += node.Num;
                        }
                    }
                    
                    if (config.Reward2 != null && config.Reward2.Count > 0)
                    {
                        var source = config.Reward2.ToList();
                        foreach (var node in source)
                        {
                            var key = (itemid)node.ItemId;
                            if (!rewardsDic.ContainsKey(key))
                            {
                                rewardsDic[key] = 0;
                            }
                            rewardsDic[key] += node.Num;
                        }
                    }
                }

                foreach (var data in rewardsDic)
                {
                    if (ToolScriptExtend.GetConfigById<item_config>((int)data.Key, out var temp))
                    {
                        rewards.Add(new reward(){item_id = data.Key,num = data.Value});
                    }
                }
                
                rewards.Sort(ChaoZhiManager.CompareRewardLogic);
                
                //----------------立即获得奖励----------------

                var quickResult = new List<reward>();
                if (ToolScriptExtend.GetTable<activity_battlepass_grade>(out var quickConfig))
                {
                    var quickList = quickConfig.Where(x => (int)x.activity_templateid == TemplateId).ToList();

                    var quickDic = new Dictionary<itemid, long>();
                    foreach (var data in quickList)
                    {
                        foreach (var node in data.reward)
                        {
                            var key = node.item_id;
                            if (!quickDic.ContainsKey(key))
                            {
                                quickDic[key] = 0;
                            }
                            quickDic[key] += node.num;
                        }
                    }
                    foreach (var data in quickDic)
                    {
                        quickResult.Add(new reward() { item_id = data.Key, num = data.Value });
                    }
                    
                    quickResult.Sort(ChaoZhiManager.CompareRewardLogic);
                }
                
                //----------------payment中的联盟宝箱奖励----------------
                var extraResult = new List<reward>();
                if (paymentData != null)
                {
                    var payData = paymentData;
                    if (payData != null && payData.diamond > 0)
                    {
                        extraResult.Add(new reward(){item_id = itemid.itemid_6,num = payData.diamond});
                    }
                    if (payData != null && payData.alliance_chest.Count > 0)
                    {
                        foreach (var chest in payData.alliance_chest)
                        {
                            extraResult.Add(new Config.reward()
                            {
                                num = 1,
                                item_id = (itemid)chest
                            });
                        }
                    }
                    extraResult.Sort(ChaoZhiManager.CompareRewardLogic); 
                }
                
                //----------------总结----------------
                var FinalList = new List<reward>();
                FinalList.AddRange(quickResult);
                FinalList.AddRange(extraResult);
                FinalList.AddRange(rewards);
                
                var rewardRoot = m_goContent.transform;
                ToolScriptExtend.RecycleOrCreate(m_goRewardItem,rewardRoot,FinalList.Count);
                for (var j = 0; j < FinalList.Count; j++)
                {
                    var info = FinalList[j];
                    var rewardChild = rewardRoot.GetChild(j);
                    var node = rewardChild.Find("node");
                    node.localScale = Vector3.one * 0.7f;
                    if (node.childCount == 0)
                    {
                        BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
                        {
                            module.SetClick(module.OpenTips);
                            ToolScriptExtend.SetItemObjTxtScale(module.gameObject, 1.3f);
                        });
                    }
                    else
                    {
                        ToolScriptExtend.SetItemObjInfo(node, node.GetChild(0).gameObject, info.item_id, (int)info.num);
                    }
                }
            }
            
            m_goEffect.SetActive(true);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnBuyClick()
        {

        }
        
        
    }
}
