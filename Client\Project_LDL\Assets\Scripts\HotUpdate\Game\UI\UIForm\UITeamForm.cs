using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Fight;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using Team;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;
using Action = System.Action;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public enum UITeamFormType
    {
        Common = 1,            // 普通队列
        TradeTruckAttack = 2,  // 货车进攻队列
        TradeTruckDefend = 3,  // 货车防守队列
        TradeTrainAttack = 4,  // 火车进攻队列
        TradeTrainDefend = 5,  // 火车防守队列
    }

    public class UITeamFormParam
    {
        public UITeamFormType TeamFormType;
        public int Index;
    }

    public partial class UITeamForm : UGuiFormEx
    {
        public UITeamFormType CurTeamFormType => m_CurTeamFormType;
        public int CurTeamTypeIndex => m_CurTeamTypeIndex;
        public List<TeamType> TeamTypes => m_TeamTypes;
        
        public int SelectType => selectType;
        private int selectType = -1;

        private UITeamFormType m_CurTeamFormType;

        private int m_CurTeamTypeIndex;
        private List<TeamType> m_TeamTypes;
        List<Dictionary<EnumBattlePos, int>> m_TeamData;
        
        private Dictionary<EnumBattlePos, UITeamFormHUD> m_HudsList;


        private bool m_TeamDataDirty = false;
        private bool m_RelationDirty = false;
        private bool m_IsManualSave = false; //是否手动保存
        
        private Tweener m_RelationTweener;

        private EnumBattleRelation m_CurRelation = EnumBattleRelation.None;

        private UITeamFormHeroContainer m_HeroContainer;

        private UITeamFormNormalTeamSwitch m_TeamSwitch;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_HeroContainer = m_goHeroContainer.GetComponent<UITeamFormHeroContainer>();
            m_TeamSwitch = m_goNormalTeamSwitch.GetComponent<UITeamFormNormalTeamSwitch>();
            
            int childCount = m_transBtnList.childCount;
            var heroData = GameEntry.LogicData.HeroData;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);

                if (i > 0)
                {
                    var icon = trans.Find("bg/icon").GetComponent<Image>();
                    var selecSp = trans.Find("selectBg/selecSp").GetComponent<Image>();
                    icon.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                    selecSp.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                }

                int index = i;
                var rectTrans = trans.GetComponent<RectTransform>();
                // rectTrans.sizeDelta = new Vector2(245, 91);

                var btn = trans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() => { OnSelectBtn(index); });
            }
        }

        private void LateUpdate()
        {
            if (m_RelationDirty)
            {
                m_RelationDirty = false;
                TryResetRelationUI();
            }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UITeamFormParam param)
            {
                m_CurTeamFormType = param.TeamFormType;
                m_CurTeamTypeIndex = param.Index;
            }
            
            m_TeamDataDirty = false;
            
            InitData();

            InitTouch();

            InitHud();

            if (selectType != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }

            //RefreshAirportAttr();


            TryResetRelationUI(true);

            m_HeroContainer.OnOpen();
            
            m_HeroContainer.OnHeroCreateCall += OnHeroCreate;
            m_HeroContainer.OnHeroDeleteCall += OnHeroRemove;

            InitUI();
            
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            
            m_HeroContainer.OnHeroCreateCall -= OnHeroCreate;
            m_HeroContainer.OnHeroDeleteCall -= OnHeroRemove;
            
            m_HeroContainer.OnClose();
            m_TeamSwitch.OnClose();
            
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            UnInitHud();
            UnInitTouch();
            UnInitUI();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void InitData()
        {
            m_IsManualSave = false;
            if (m_CurTeamFormType == UITeamFormType.Common)
            {
                m_TeamTypes = new List<TeamType>
                {
                    TeamType.Common1,
                    TeamType.Common2,
                    TeamType.Common3,
                    TeamType.Common4
                };
            }
            else if (m_CurTeamFormType == UITeamFormType.TradeTruckAttack)
            {
                m_TeamTypes = new List<TeamType>
                {
                    TeamType.TradeVanAttack1,
                    TeamType.TradeVanAttack2,
                    TeamType.TradeVanAttack3,
                    TeamType.TradeVanAttack4
                };
            }
            else if (m_CurTeamFormType == UITeamFormType.TradeTruckDefend)
            {
                m_IsManualSave = true;
                m_TeamTypes = new List<TeamType>
                {
                    TeamType.TradeVanDefend1,
                    TeamType.TradeVanDefend2,
                    TeamType.TradeVanDefend3,
                    TeamType.TradeVanDefend4
                };
            }
            else if (m_CurTeamFormType == UITeamFormType.TradeTrainAttack)
            {
                m_TeamTypes = new List<TeamType>
                {
                    TeamType.TradeTrainAttack1,
                    TeamType.TradeTrainAttack2,
                    TeamType.TradeTrainAttack3,
                };
            }
            else if (m_CurTeamFormType == UITeamFormType.TradeTrainDefend)
            {
                m_TeamTypes = new List<TeamType>
                {
                    TeamType.TradeTrainDefend1,
                    TeamType.TradeTrainDefend2,
                    TeamType.TradeTrainDefend3
                };
            }

            if (m_TeamData == null || !m_IsManualSave)
            {
                m_TeamData = new List<Dictionary<EnumBattlePos, int>>();
                for (int i = 0; i < m_TeamTypes.Count; i++)
                {
                    var teamDic = GameEntry.LogicData.TeamData.GetTeamDic(m_TeamTypes[i]);
                    m_TeamData.Add(teamDic);
                }
            }
        }
        
        private void InitUI()
        {
            m_btnManualSave.gameObject.SetActive(m_IsManualSave);
            m_txtNormalTitle.text = ToolScriptExtend.GetLang(1009);
            UpdatePower();

            if (m_CurTeamFormType == UITeamFormType.Common)
            {
                m_TeamSwitch.OnOpen(this);
                m_TeamSwitch.SwitchTo(m_CurTeamTypeIndex);
                SwitchTeamIndex(m_CurTeamTypeIndex,true);
            }
            else
            {
                m_TeamSwitch.OnOpen(this);
                m_TeamSwitch.SwitchTo(m_CurTeamTypeIndex);
                SwitchTeamIndex(m_CurTeamTypeIndex,true);
            }
        }

        private void UnInitUI()
        {
            if (m_CurTeamFormType == UITeamFormType.Common)
            {
                m_TeamSwitch.OnClose();
            }
            else
            {
                m_TeamSwitch.OnClose();
            }
        }

        public void SwitchTeamIndex(int index,bool force = false)
        {
            if (m_CurTeamTypeIndex != index || force)
            {
                m_CurTeamTypeIndex = index;    
                m_HeroContainer.RemoveAllHero();
                
                var heroData = m_TeamData[m_CurTeamTypeIndex];
                foreach (var item in heroData)
                {
                    m_HeroContainer.CreateHero(item.Key, item.Value);
                }

                OnUpdateInfo();
            }

            UpdateTeamIndex();
        }

        public void UpdateTeamIndex()
        {
            string path = m_CurTeamTypeIndex switch
            {
                0 => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_bg_1.png",
                1 => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_bg_2.png",
                2 => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_bg_3.png",
                3 => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_bg_4.png",
            };
            if (!string.IsNullOrEmpty(path))
            {
                m_imgTeamIndex.SetImage(path);
            }
        }
        
        private void UpdatePower()
        {
            double power = m_HeroContainer.GetPowerFromHeroModule();
            m_txtBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
        }
        
        private void OnBtnPowerLeftClick()
        {
            m_goPower.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnPowerRightClick()
        {
        }

        private void OnBtnLeftRelationClick()
        {
            var list = m_HeroContainer.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            
            var relationItem = m_goRelation.GetComponent<UIRelationItem>();
            relationItem?.SetRelation(list, relation);
            
            m_goRelation.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnRightRelationClick()
        {
        }

        private void OnBtnExitClick()
        {
            if (!m_IsManualSave)
            {
                TrySyncTeam(() => {});
            }
            
            Close();
        }
        
        private void OnBtnManualSaveClick()
        {
            TrySyncTeam(() =>
            {
                // 手动保存后总是触发事件，用于刷新界面显示
                var currentTeamType = m_TeamTypes[m_CurTeamTypeIndex];
                GameEntry.Event.Fire(TeamChangeEventArgs.EventId, TeamChangeEventArgs.Create(currentTeamType));
            });
            Close();
        }

        private void OnBtnAirSupportClick()
        {
            // m_goAirSupport.SetActive(true);
            // m_btnMask.gameObject.SetActive(true);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUavTip,m_btnAirSupport.gameObject);
        }

        private void OnBtnUnknowClick()
        {
        }

        private void OnBtnMaskClick()
        {
            m_goPower.SetActive(false);
            m_goRelation.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
        }

        private void OnBtnMaskAirSupportClick()
        {
            // m_goPopupAirSupportAttr.SetActive(false);
            // m_btnMaskAirSupport.gameObject.SetActive(false);
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnSelectBtn(int index)
        {
            if (selectType == index)
                return;

            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                // rectTrans.sizeDelta = new Vector2(260, 91);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            if (selectType > -1)
            {
                trans = m_transBtnList.GetChild(selectType);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    // rectTrans.sizeDelta = new Vector2(245, 91);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }

            selectType = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            List<HeroModule> heroList = GetHeroList();

            Transform rootTrans = m_scrollview.content;
            ToolScriptExtend.RecycleOrCreate(m_transHeroItem.gameObject, rootTrans, heroList.Count);
            for (int i = 0; i < heroList.Count; i++)
            {
                var item = rootTrans.GetChild(i);
                OnUpdateItem(item, heroList, i);
            }
        }

        private List<HeroModule> GetHeroList()
        {
            List<HeroModule> heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true,
                (heroModule) => heroModule.IsActive);
            return heroList;
        }

        private void OnUpdateItem(Transform item, List<HeroModule> heroList, int index)
        {
            var heroVo = heroList[index];
            OnUpdateItem(item, heroVo);
        }

        private void OnUpdateItem(Transform item, HeroModule heroVo)
        {
            var heroItem = item.GetComponent<UIHeroItem>();
            heroItem.Refresh(heroVo);

            bool inSelected = IsInBattle((int)heroVo.id);
            heroItem.SetSelected(inSelected);

            // 检查英雄是否在被占用的货车编队中
            bool isOccupied = IsHeroInOccupiedTruckTeam((int)heroVo.id);
            heroItem.SetOccupied(isOccupied);

            if (!inSelected)
                heroItem.SetTeamIndex(GetTeamIndex((int)heroVo.id));
            else
                heroItem.SetTeamIndex(null);
            
            heroItem.RemoveAllClickListeners();
            heroItem.AddClickListener(() =>
            {
                int heroId = (int)heroVo.id;
                if (m_HeroContainer.IsInBattle(heroId,out EnumBattlePos pos))
                {
                    RemoveHero(pos, heroId);
                    OnUpdateItem(item, heroVo);
                }
                else
                {
                    var emptyPos = m_HeroContainer.GetEmptyPosition();
                    if (emptyPos != null)
                    {
                        var heroCurTeam = GetTeamIndex(heroId);  
                        if (heroCurTeam!=null)
                        {
                            var heroModule = GameEntry.LogicData.HeroData.GetHeroModule((Config.itemid)heroId);
                            
                            GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                            {
                                Title = ToolScriptExtend.GetLang(1100147),
                                Content = ToolScriptExtend.GetLangFormat(1326,heroModule.Name,(heroCurTeam+1).ToString()),
                                ConfirmText = ToolScriptExtend.GetLang(1100144),
                                CancelText = ToolScriptExtend.GetLang(1100143),
                                OnClickConfirm = (data) =>
                                {
                                    //将其从别的队伍中移除
                                    var tempTeamDic = m_TeamData[heroCurTeam.Value];
                                    foreach (var item in tempTeamDic)
                                    {
                                        if (item.Value == heroId)
                                        {
                                            tempTeamDic.Remove(item.Key);
                                            break;
                                        }
                                    }
                                    
                                    CreateHero(emptyPos.Value, heroId);
                                    OnUpdateItem(item, heroVo);
                                },
                            });
                                    
                        }
                        else
                        {
                            CreateHero(emptyPos.Value, heroId);
                            OnUpdateItem(item, heroVo);
                        }
                        
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(1327),
                        });
                    }
                }
            });
        }

        private int? GetTeamIndex(int heroId)
        {
            for (int i = 0; i < m_TeamData.Count; i++)
            {
                var teamDic = m_TeamData[i];
                if (teamDic.ContainsValue(heroId))
                {
                    return i;
                }
            }
            return null;
        }
        
        private bool IsInBattle(int heroId)
        {
            var heroData = m_TeamData[m_CurTeamTypeIndex];
            if (heroData.ContainsValue(heroId))
            {
                return true;
            }
            return false;
        }

        private void RefreshAirportAttr()
        {

        }

        /// <summary>
        /// 检查英雄是否在被占用的货车编队中
        /// </summary>
        /// <param name="heroId">英雄ID</param>
        /// <returns>是否被占用</returns>
        private bool IsHeroInOccupiedTruckTeam(int heroId)
        {
            // 仅对贸易货车防守编队进行检查
            if (m_CurTeamFormType != UITeamFormType.TradeTruckDefend)
                return false;

            // 获取被占用的编队类型
            var occupiedTeams = GameEntry.TradeTruckData.GetOccupiedTradeVanDefendTeams();
            if (occupiedTeams.Count == 0)
                return false;

            // 通过 TeamData 的 RequestTeamQuery 查询英雄是否在被占用的编队中
            foreach (var teamType in occupiedTeams)
            {
                var teamDic = GameEntry.LogicData.TeamData.GetTeamDic(teamType);
                if (teamDic != null && teamDic.ContainsValue(heroId))
                {
                    return true;
                }
            }

            return false;
        }

        private void TrySyncTeam(Action callBack)
        {
            if (!m_TeamDataDirty)
            {
                callBack?.Invoke();
                return;
            }

            m_TeamDataDirty = false;
        
            List<FormationTeam> formationTeams = new List<FormationTeam>(); 
            for (int i = 0; i < m_TeamTypes.Count; i++)
            {
                FormationTeam formationTeam = new FormationTeam();
                formationTeam.TeamType = m_TeamTypes[i];
                var teamDic = m_TeamData[i];
                if (teamDic != null && teamDic.Count > 0)
                {
                    
                    foreach (var item in teamDic)
                    {
                        FormationHero formationHero = new FormationHero();
                        formationHero.Pos = (int)item.Key;
                        formationHero.HeroId = (itemid)item.Value;
                        
                        formationTeam.Heroes.Add(formationHero);
                    }

                }
                formationTeams.Add(formationTeam);
            }

            if (formationTeams.Count > 0)
            {
                GameEntry.LogicData.TeamData.TeamModify(formationTeams,callBack);
            }else{
                callBack?.Invoke();
            }
        }

        private void CreateHero(EnumBattlePos pos,int heroId)
        {
            //英雄数据移动
            var teamDic = m_TeamData[m_CurTeamTypeIndex];
            teamDic[pos] = heroId;
            m_HeroContainer.CreateHero(pos, heroId);
            
            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        private void RemoveHero(EnumBattlePos pos,int heroId)
        {
            //英雄数据移动
            var teamDic = m_TeamData[m_CurTeamTypeIndex];
            teamDic.Remove(pos);
            
            m_HeroContainer.RemoveHero(heroId);
            
            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }
        
        private void RemoveHero(UITeamFormHero hero)
        {
            RemoveHero(hero.BattlePos,hero.HeroId);
        }
        

        #region touch 相关逻辑

        [CanBeNull] private UITeamFormHero m_OpDragBattleHero;
        private EnumBattlePos? m_OpCurPos; //当前手指所在区域

        private void InitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped += OnTapped;
            controller.dragged += OnDragged;
            controller.released += OnReleased;
            controller.pressed += OnPressed;
        }

        private void UnInitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped -= OnTapped;
            controller.dragged -= OnDragged;
            controller.released -= OnReleased;
            controller.pressed -= OnPressed;
        }

        private void OnTapped(PointerActionInfo pointer)
        {
            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);
            if (pos != null)
            {
                var hero = m_HeroContainer.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    RemoveHero(hero.BattlePos,hero.HeroId);
                    OnUpdateInfo();
                }
            }
        }

        private void OnDragged(PointerActionInfo pointer)
        {
            if (m_OpDragBattleHero == null)
                return;

            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            m_OpDragBattleHero.SetPosition(worldPos);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);
            if (pos == null)
            {
                //没有在任何区域内部
                MoveBackCache();
            }
            else
            {
                //移动到了新区域
                if (m_OpDragBattleHero.BattlePos == pos.Value)
                {
                    //新区域是我自己
                    MoveBackCache();
                }
                else
                {
                    if (m_OpCurPos == pos.Value)
                    {
                        //已经互换 什么也不做
                    }
                    else
                    {
                        MoveBackCache();
                        MoveAndCache(pos.Value);
                    }
                }
            }
        }

        private void OnReleased(PointerActionInfo pointer)
        {
            if (m_OpCurPos != null && m_OpDragBattleHero != null)
            {
                int heroIdA = m_OpDragBattleHero.HeroId;
                EnumBattlePos posA = m_OpDragBattleHero.BattlePos;
                int? heroIdB = null;
                EnumBattlePos posB = m_OpCurPos.Value;

                //删除原有英雄
                if (m_OpDragBattleHero != null)
                    RemoveHero(m_OpDragBattleHero);
                //删除目标位置英雄
                var heroTarget = m_HeroContainer.GetBattleHero(m_OpCurPos.Value);
                if (heroTarget != null)
                {
                    heroIdB = heroTarget.HeroId;
                    RemoveHero(heroTarget);
                }

                //创建新英雄
                CreateHero(posB, heroIdA);
                if (heroIdB != null)
                    CreateHero(posA, heroIdB.Value);

                m_OpCurPos = null;
                m_OpDragBattleHero = null;
            }
            else
            {
                if (m_OpDragBattleHero != null)
                    m_OpDragBattleHero.MoveBack();
                m_OpDragBattleHero = null;

                MoveBackCache();
            }
        }

        private void OnPressed(PointerActionInfo pointer)
        {
            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);

            if (pos != null)
            {
                var hero = m_HeroContainer.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_OpDragBattleHero = hero;
                }
            }
        }

        private void MoveBackCache()
        {
            if (m_OpCurPos != null)
            {
                //将之前的英雄归位
                var tempHero = m_HeroContainer.GetBattleHero(m_OpCurPos.Value);
                if (tempHero != null)
                    tempHero.MoveBack();
                m_OpCurPos = null;
            }
        }

        private void MoveAndCache(EnumBattlePos pos)
        {
            if (m_OpDragBattleHero == null)
                return;

            var hero = m_HeroContainer.GetBattleHero(pos);
            if (hero != null)
            {
                hero.MoveTo(m_OpDragBattleHero.BattlePos);
            }

            m_OpCurPos = pos;
        }

        #endregion

        #region 战斗单位头部Hud

        public void InitHud()
        {
            m_HudsList = new Dictionary<EnumBattlePos, UITeamFormHUD>();

            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if((int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                var hud = CreateHud(pos);
                var hero = m_HeroContainer.GetBattleHero(pos);
                if (hero != null)
                    hud.Show();
                else
                    hud.Hide();
            }
        }
        
        private void OnHeroRemove(EnumBattlePos pos,int heroId)
        {
            if (m_HudsList.TryGetValue(pos, out UITeamFormHUD choose))
            {
                choose.Hide();
            }

            UpdatePower(); 
        }

        private void OnHeroCreate(EnumBattlePos pos,int heroId)
        {
            if (m_HudsList.TryGetValue(pos, out UITeamFormHUD choose))
            {
                choose.Show();
            }

            UpdatePower();
        }

        public void UnInitHud()
        {
            foreach (var item in m_HudsList)
            {
                Destroy(item.Value.gameObject);
            }

            m_HudsList.Clear();
            m_HudsList = null;
        }

        private UITeamFormHUD CreateHud(EnumBattlePos pos)
        {
            var newItem = Instantiate(m_transHudItem);
            newItem.transform.parent = m_transHudParent;
            newItem.transform.localPosition = Vector3.one;
            newItem.transform.localScale = Vector3.one;

            var hud = newItem.gameObject.GetOrAddComponent<UITeamFormHUD>();
            hud.Init(m_HeroContainer, pos);
            m_HudsList.Add(pos, hud);
            return hud;
        }

        #endregion


        #region 羁绊相关

        private void TryResetRelationUI(bool force = false)
        {
            var list = m_HeroContainer.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            if (m_CurRelation!=relation || force)
            {
                m_CurRelation = relation;
                SetRelationIcon();
            }
        }

        private void SetRelationIcon()
        {
            if(m_RelationTweener!=null && m_RelationTweener.IsPlaying())
                m_RelationTweener.Kill();
            m_btnLeftRelation.transform.localScale = Vector3.one;
            m_RelationTweener = m_btnLeftRelation.transform.DOPunchScale(Vector3.one * 0.5f, 0.3f);
            
            if (m_CurRelation == EnumBattleRelation.None)
            {
                m_imgLeftRelationA.gameObject.SetActive(false);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            } 
            else if (m_CurRelation == EnumBattleRelation.Relation3_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
            else if (m_CurRelation == EnumBattleRelation.Relation3_2)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(true);
                m_imgLeftRelationC.SetImageGray(true);
            }else if (m_CurRelation == EnumBattleRelation.Relation4_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }else if (m_CurRelation == EnumBattleRelation.Relation5_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
        }
        
        #endregion
    }
}