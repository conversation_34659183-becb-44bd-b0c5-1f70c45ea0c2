using System;
using UnityEngine;
using UnityEngine.UI;
using Game.Hotfix.Config;
using DG.Tweening;
using System.Collections.Generic;

namespace Game.Hotfix
{
    // Item类
    public class UIItemModule : MonoBehaviour
    {
        public int UID { get; set; } // 物品唯一标识符
        public string Name { get; set; } // 物品名称
        public string Description { get; set; } // 物品描述
        public ItemType Type { get; set; } // 物品类型
        public int Quality { get; set; } // 物品质量
        public long Count { get; set; } // 物品数量
        public bool IsStackable { get; set; } // 物品是否可堆叠
        public int ItemType { get; set; }//物品类型
        public GameObject Select { get; set; } //选中
        public int level = 0;//等级
        public GameObject levelNode;
        public GameObject extra_desc;
        public Action clickFun;
        public itemid ItemId { get; set; }
        public GameObject itemObj = null;
        public ItemModule itemModule;
        public UIImage quality;
        public UIImage qualityEquipment;

        public GameObject equipment;
        public GameObject equipmentMaking;
        public Transform equipmentMakingIcon;
        public GameObject equipmentMakingFinish;
        public GameObject equipmentSelect;
        public UIText txtEquipmentLevel;
        public GameObject equipmentRedpoint;
        public GameObject equipmentStar;
        public List<GameObject> equipmentGemList = new();
        public UIImage heroBorder;
        public UIImage heroHead;
        public GameObject equipmentCanMake;
        public GameObject equipmentLock;
        Sequence equipmentMakingSequence;
        public GameObject plunder;

        public virtual void Init(Transform parent, itemid itemId, long count)
        {
            ItemId = itemId;
            Count = count;
            InitConfigData();
            itemObj.transform.SetParent(parent, false);
            itemObj.transform.localScale = Vector3.one;
            UIButton uiButton = itemObj.GetComponent<UIButton>();
            uiButton.onClick.AddListener(ItemClick);
            levelNode = itemObj.transform.Find("levelNode").gameObject;
            extra_desc = itemObj.transform.Find("extra_desc").gameObject;
        }
        public virtual void IsSelect(bool value)
        {
            if (Select == null)
            {
                Select = itemObj.transform.Find("select").gameObject;
            }
            if (value)
            {
                Select.SetActive(true);
            }
            else
            {
                Select.SetActive(false);
            }
        }
        public virtual void SetData(itemid id, long count)
        {
            // 如果itemModule为空，创建一个新的ItemModule实例
            if (itemModule == null)
            {
                itemModule = new ItemModule();
            }

            // 设置ItemModule的数据
            itemModule.ItemId = id;
            itemModule.Count = count;
        }
        public void SetScale(float size)
        {
            if (itemObj)
            {
                itemObj.transform.localScale = new Vector3(size, size, 1);

                var extraDesc = extra_desc.transform.Find("Image/extraDesc").GetComponent<UIText>();
                var txtlevel = levelNode.transform.Find("Image/txtlevel").GetComponent<UIText>();
                var count = itemObj.transform.Find("count").GetComponent<Text>();
                extraDesc.transform.localScale = new Vector3(1 / size, 1 / size, 1);
                extraDesc.rectTransform.sizeDelta = new Vector2(205 * size, 60);
                txtlevel.transform.localScale = new Vector3(1 / size, 1 / size, 1);
                txtlevel.rectTransform.sizeDelta = new Vector2(205 * size, 80);
                count.transform.localScale = new Vector3(1 / size, 1 / size, 1);
                count.rectTransform.sizeDelta = new Vector2(185 * size, 75);
                if (size <= 0.4f)
                {
                    extraDesc.fontSize = 20;
                    txtlevel.fontSize = 20;
                    count.fontSize = 26;
                    count.resizeTextMinSize = 20;
                    count.resizeTextMaxSize = 26;
                }
                else if (size <= 0.5f)
                {
                    extraDesc.fontSize = 24;
                    txtlevel.fontSize = 24;
                    count.fontSize = 30;
                    count.resizeTextMinSize = 20;
                    count.resizeTextMaxSize = 30;
                }
                else if (size <= 0.6f)
                {
                    extraDesc.fontSize = 26;
                    txtlevel.fontSize = 26;
                    count.fontSize = 36;
                    count.resizeTextMinSize = 20;
                    count.resizeTextMaxSize = 36;
                }
                else if (size <= 0.65f)
                {
                    extraDesc.fontSize = 30;
                    txtlevel.fontSize = 30;
                    count.fontSize = 40;
                    count.resizeTextMinSize = 20;
                    count.resizeTextMaxSize = 40;
                }
                else
                {
                    extraDesc.fontSize = 35;
                    txtlevel.fontSize = 35;
                    count.fontSize = 46;
                    count.resizeTextMinSize = 20;
                    count.resizeTextMaxSize = 46;
                }
            }
        }
        public virtual void SetIconScale(Vector3 scale)
        {
            Image icon = itemObj.transform.Find("icon").GetComponent<Image>();
            icon.transform.SetLocalScale(scale.x, scale.y, scale.z);
        }

        public virtual void IsShowCount(bool value)
        {
            Transform t_count = itemObj.transform.Find("count");
            t_count.gameObject.SetActive(value);
        }
        public virtual void SetLevel(int _level)
        {
            level = _level;
            levelNode.SetActive(level > 1);
        }
        public virtual void ShowPro(float value)
        {
            Transform ratio = itemObj.transform.Find("ratio");
            ratio.gameObject.SetActive(true);
            UIText txt = ratio.transform.GetComponent<UIText>();
            txt.text = value.ToString() + "%";
        }
        public virtual void SetClick(Action fun)
        {
            UIButton uiButton = itemObj.GetComponent<UIButton>();
            uiButton.onClick.RemoveAllListeners();
            uiButton.onClick.AddListener(ItemClick);
            clickFun = fun;
        }
        // 使用物品的方法
        public virtual void Use()
        {
            Console.WriteLine($"使用了物品: {Name}");

        }

        public virtual void ItemClick()
        {
            Debug.Log("ItemClick");
            // Text count = itemObj.transform.Find("count").GetComponent<Text>();
            // count.text =(Count*2).ToString();
            clickFun?.Invoke();
        }

        public virtual void OpenTxtTips()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIItemTextTip, this);
        }
        public virtual void OpenTips()
        {
            OpenTxtTips();
        }
        // 显示物品信息的方法  
        public virtual void DisplayInfo()
        {
            Text count = itemObj.transform.Find("count").GetComponent<Text>();
            var itemCount = itemModule.Count;
            count.text = itemCount <= 0 ? "" : ToolScriptExtend.FormatNumberWithUnit(itemModule.Count);
            item_config config = itemModule.GetItemConfig();
            quality.SetImage(GetQualityEquipmentPath((int)config.quality));
            qualityEquipment.SetImage(GetQualityEquipmentPath((int)config.quality));
            Image icon = itemObj.transform.Find("icon").GetComponent<Image>();
            icon.SetImage(config.icon, false);

            if (ItemType == (int)itemtype.itemtype_heroequipment)
            {

            }
            if (extra_desc == null)
            {
                extra_desc = itemObj.transform.Find("extra_desc").gameObject;
            }
            if (config.extra_desc > 0)
            {
                extra_desc.SetActive(true);
                UIText extraDesc = extra_desc.transform.Find("Image/extraDesc").GetComponent<UIText>();
                extraDesc.text = ToolScriptExtend.GetLang(config.extra_desc);
            }
            else
            {
                extra_desc.SetActive(false);
            }
        }

        public virtual void InitConfigData()
        {
            item_config config = itemModule.GetItemConfig();
            Quality = (int)config.quality;
            Name = ToolScriptExtend.GetLang(config.name);
            Description = ToolScriptExtend.GetLang(config.desc);
            ItemType = (int)config.item_type;
        }

        public string GetItemPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                Config.quality.quality_white => "Sprite/ui_public/pingzhikuang_zhuangbei_grey.png",
                Config.quality.quality_green => "Sprite/ui_public/pingzhikuang_zhuangbei_green.png",
                Config.quality.quality_blue => "Sprite/ui_public/pingzhikuang_zhuangbei_blue.png",
                Config.quality.quality_purple => "Sprite/ui_public/pingzhikuang_zhuangbei_purple.png",
                Config.quality.quality_orange => "Sprite/ui_public/pingzhikuang_zhuangbei_yellow.png",
                Config.quality.quality_red => "Sprite/ui_public/pingzhikuang_zhuangbei_red.png",
                _ => "Sprite/ui_public/pingzhikuang_zhuangbei_blue.png",
            };
        }

        public string GetQualityEquipmentPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                Config.quality.quality_white => "Sprite/ui_public/pingzhikuang_zhuangbei_grey.png",
                Config.quality.quality_green => "Sprite/ui_public/pingzhikuang_zhuangbei_green.png",
                Config.quality.quality_blue => "Sprite/ui_public/pingzhikuang_zhuangbei_blue.png",
                Config.quality.quality_purple => "Sprite/ui_public/pingzhikuang_zhuangbei_purple.png",
                Config.quality.quality_orange => "Sprite/ui_public/pingzhikuang_zhuangbei_yellow.png",
                Config.quality.quality_red => "Sprite/ui_public/pingzhikuang_zhuangbei_red.png",
                _ => "Sprite/ui_public/pingzhikuang_zhuangbei_grey.png",
            };
        }

        public static int GetItemQuality(int itemId)
        {

            return 0;
        }

        public item_config GetItemConfig()
        {
            return GameEntry.LDLTable.GetTableById<item_config>(itemModule.ItemId);
        }

        // 增加物品数量的方法
        public void AddQuantity(int amount)
        {
            if (IsStackable)
            {
                Count += amount;
                Console.WriteLine($"增加了 {amount} 个 {Name}，当前数量: {Count}");
            }
            else
            {
                Console.WriteLine($"{Name} 不可堆叠，无法增加数量。");
            }
        }

        // 减少物品数量的方法
        public void RemoveQuantity(int amount)
        {
            if (Count >= amount)
            {
                Count -= amount;
                Console.WriteLine($"减少了 {amount} 个 {Name}，当前数量: {Count}");
            }
            else
            {
                Console.WriteLine($"数量不足，无法减少 {amount} 个 {Name}。");
            }
        }

        /// <summary>
        /// 设置装备制作动画
        /// </summary>
        public void SetEquipmentMakingIconAnim()
        {
            if (equipmentMakingIcon == null) return;

            if (equipmentMakingSequence == null)
            {
                float duration = 0.18f;
                Sequence sequence = DOTween.Sequence();
                equipmentMakingSequence = sequence;
                sequence.Insert(0, equipmentMakingIcon.DOLocalRotate(new Vector3(0, 0, 35), duration));
                sequence.Insert(duration, equipmentMakingIcon.DOLocalRotate(new Vector3(0, 0, 0), duration));
                sequence.Insert(duration * 2, equipmentMakingIcon.DOLocalRotate(new Vector3(0, 0, 35), duration));
                sequence.Insert(duration * 3, equipmentMakingIcon.DOLocalRotate(new Vector3(0, 0, 0), duration));
                sequence.AppendInterval(0.5f);
                sequence.SetLoops(-1);
            }
        }

        /// <summary>
        /// 设置装备选中
        /// </summary>
        public void SelectEquipment(bool value)
        {
            equipmentSelect.SetActive(value);
        }

        /// <summary>
        /// 刷新装备红点
        /// </summary>
        public void RefreshEquipmentRedpoint(bool value)
        {
            equipmentRedpoint.SetActive(value);
        }

        /// <summary>
        /// 刷新装备星级
        /// </summary>
        /// <param name="value">星级</param>
        public void RefreshEquipmentStar(int value, bool isShow)
        {
            if (equipmentStar == null) return;
            equipmentStar.SetActive(isShow);
            for (int i = 0; i < equipmentGemList.Count; i++)
            {
                equipmentGemList[i].SetActive(i < value);
            }
        }

        /// <summary>
        /// 刷新装备品质
        /// </summary>
        /// <param name="quality">品质</param>
        public void RefreshEquipmentQuality(int quality)
        {
            qualityEquipment.SetImage(GetQualityEquipmentPath(quality));
        }

        /// <summary>
        /// 切换品质图标
        /// </summary>
        /// <param name="isEquipment">是否装备</param>
        public void SwitchQualityIcon(bool isEquipment)
        {
            quality.gameObject.SetActive(!isEquipment);
            qualityEquipment.gameObject.SetActive(isEquipment);
        }

        /// <summary>
        /// 刷新英雄头像
        /// </summary>
        /// <param name="quality">品质框</param>
        /// <param name="heroHeadPath">英雄头像路径</param>
        public void RefreshHeroHead(int quality, string heroHeadPath)
        {
            heroBorder.SetImage(ToolScriptExtend.GetWearingHeroQualityBg((quality)quality));
            heroHead.SetImage(heroHeadPath);
            heroBorder.gameObject.SetActive(true);
        }

        /// <summary>
        /// 背包内刷新
        /// </summary>
        public void RefreshInBag()
        {
            item_config itemConfig = itemModule.GetItemConfig();
            if (itemConfig.item_type == itemtype.itemtype_heroequipment)
            {
                if (itemModule is EquipmentModule equipment)
                {
                    IsShowCount(false);
                    SwitchQualityIcon(true);
                    txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                    txtEquipmentLevel.gameObject.SetActive(equipment.EquipmentQuality != (int)Config.quality.quality_green);
                    RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                    RefreshEquipmentQuality(equipment.EquipmentQuality);
                    if (equipment.target_id > 0)
                    {
                        HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule(equipment.target_id);
                        if (heroModule != null)
                        {
                            RefreshHeroHead(heroModule.Quality, heroModule.HeroHead);
                        }
                    }
                    else
                    {
                        heroBorder.gameObject.SetActive(false);
                    }
                    heroBorder.rectTransform.anchoredPosition = new Vector2(-22f, 22f);
                }
            }
            else
            {
                IsShowCount(true);
                SwitchQualityIcon(false);
                txtEquipmentLevel.text = string.Empty;
                txtEquipmentLevel.gameObject.SetActive(false);
                equipmentStar.SetActive(false);
                heroBorder.gameObject.SetActive(false);
            }
        }

        //置灰
        public void SetGrey(bool isGrey)
        {
            ToolScriptExtend.SetGameObjectGrey(transform, isGrey);
        }

       
    }
}
