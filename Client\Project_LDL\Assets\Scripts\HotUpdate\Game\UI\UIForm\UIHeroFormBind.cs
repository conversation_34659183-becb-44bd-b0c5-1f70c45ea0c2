using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIHeroForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnRecruit;
        [SerializeField] private UIButton m_btnEquipBetter;
        [SerializeField] private UIButton m_btnMask;
        [SerializeField] private UIButton m_btnSwitch;
        [SerializeField] private UIButton m_btnMaskDropdown;
        [SerializeField] private UIButton m_btnDropdown;

        [SerializeField] private UIText m_txtDropdown;

        [SerializeField] private UIImage m_imgDropdown;

        [SerializeField] private ScrollRect m_scrollview;

        [SerializeField] private Transform m_transBtnList;
        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Mosframe.TableView m_TableViewH;
        [SerializeField] private Mosframe.TableView m_TableViewV;
        [SerializeField] private Mosframe.CCTableViewController m_TableViewD;
        [SerializeField] private GameObject m_goRecommond;
        [SerializeField] private GameObject m_goSwitchOn;
        [SerializeField] private GameObject m_goSwitchOff;
        [SerializeField] private GameObject m_goDropdownOption;
        [SerializeField] private Transform m_transDropdownOption;

        void InitBind()
        {
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnRecruit.onClick.AddListener(OnBtnRecruitClick);
            m_btnEquipBetter.onClick.AddListener(OnBtnEquipBetterClick);
            m_btnMask.onClick.AddListener(OnBtnMaskClick);
            m_btnSwitch.onClick.AddListener(OnBtnSwitchClick);
            m_btnMaskDropdown.onClick.AddListener(OnBtnMaskDropdownClick);
            m_btnDropdown.onClick.AddListener(OnBtnDropdownClick);
        }
    }
}
