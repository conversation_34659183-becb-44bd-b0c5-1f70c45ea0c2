using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainStationPlatformForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnTrainHead;
        [SerializeField] private UIButton m_btnVIP;
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnRefreshGoods;
        [SerializeField] private UIButton m_btnThanksList;
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnTopPage;
        [SerializeField] private UIButton m_btnThumbsUpAngel;
        [SerializeField] private UIButton m_btnBuffTip;
        [SerializeField] private UIButton m_btnMask;

        [SerializeField] private UIText m_txtWaitVIP;
        [SerializeField] private UIText m_txtContractCount;
        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtTime;
        [SerializeField] private UIText m_txtBuffTipTitle;
        [SerializeField] private UIText m_txtBuffTipContent;
        [SerializeField] private UIText m_txtTip;

        [SerializeField] private UIImage m_imgIconVIP;

        [SerializeField] private ScrollRect m_scrollview;

        [SerializeField] private RectTransform m_rectContent;
        [SerializeField] private RectTransform m_rectBg;
        [SerializeField] private RectTransform m_rectTrainGuard;
        [SerializeField] private RectTransform m_rectStationPlatform;
        [SerializeField] private GameObject m_goPlayerTrainHead;
        [SerializeField] private Transform m_transCarriage;
        [SerializeField] private GameObject m_goWelcome;
        [SerializeField] private GameObject m_goPlayerInfoHead;
        [SerializeField] private Transform m_transRewardParent;
        [SerializeField] private Transform m_transReward;
        [SerializeField] private GameObject m_goBottomTip;
        [SerializeField] private GameObject m_goItemEffect;
        [SerializeField] private GameObject m_goGuardianAngel;
        [SerializeField] private Transform m_transBuffItem;
        [SerializeField] private Transform m_transBuff;
        [SerializeField] private GameObject m_goBuffTip;
        [SerializeField] private GameObject m_goBuffTipArrow;
        [SerializeField] private GameObject m_goTip;

        void InitBind()
        {
            m_btnTrainHead.onClick.AddListener(OnBtnTrainHeadClick);
            m_btnVIP.onClick.AddListener(OnBtnVIPClick);
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnRefreshGoods.onClick.AddListener(OnBtnRefreshGoodsClick);
            m_btnThanksList.onClick.AddListener(OnBtnThanksListClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnTopPage.onClick.AddListener(OnBtnTopPageClick);
            m_btnThumbsUpAngel.onClick.AddListener(OnBtnThumbsUpAngelClick);
            m_btnBuffTip.onClick.AddListener(OnBtnBuffTipClick);
            m_btnMask.onClick.AddListener(OnBtnMaskClick);
        }
    }
}
