using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerForm : UGuiFormEx
    {
        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        //-------------------------第一个标签-------------------------
        private class DayNode
        {
            public int day;//第几天
            public GameObject offBg;//普通页签状态
            public GameObject lockBg;//锁定页签状态
            public GameObject onBg;//选中页签状态（已解锁）
            public GameObject lockOnBg;//选中页签状态（未解锁）
            public UIButton btn;//点击按钮
        }
        private List<DayNode> dayNodeList = new List<DayNode>();
        private int curSelectDay = -1;
        private UISwitchTagGroup SwitchTargetTaskGroup;
        private List<activity_battlepass_task> configTasks = new List<activity_battlepass_task>();
        private ScrollRect scrollTargetTask;
        private Transform scrollTargetTaskRoot;
        private float taskItemHeight = 200;
        private float taskSpacing = 10;
        private int checkDayNodeIndex = 1;
        
        //-------------------------第二个标签-------------------------
        
        // [SerializeField] private TableViewV m_ScoreTableView;
        // [SerializeField] private RectTransform cycleContent;
        // [SerializeField] private ScrollRect cycleScroll;
        // [SerializeField] private Slider m_sliderProgress;
        // [SerializeField] private ScrollRect sliderScroll;
        // [SerializeField] private GameObject m_goScoreRewardItem;
        // [SerializeField] private GameObject m_goTaskReward;
        
        
        //-------------------------第三个标签-------------------------
        
        private UISwitchTagGroup SwitchTagGroup;
        private List<int> uniqueIdList = new List<int>();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            CheckMultiLanguage(gameObject);
            scrollTargetTask = m_goTargetTask.transform.Find("Scroll View").GetComponent<ScrollRect>();
            scrollTargetTaskRoot = scrollTargetTask.content.transform;
            
            SwitchTagGroup = m_goTagGroup.GetComponent<UISwitchTagGroup>();
            taskItemHeight = m_goTaskItem.GetComponent<RectTransform>().rect.height;
            taskSpacing = scrollTargetTaskRoot.GetComponent<VerticalLayoutGroup>().spacing;
            
            var nameList = new List<string>()
            {
                ToolScriptExtend.GetLang(1100480), //"先锋目标", 
                ToolScriptExtend.GetLang(1100481), //"先锋战令"
                ToolScriptExtend.GetLang(1100482), //"先锋指挥官"
            };
            SwitchTagGroup.Init(m_goTagItem, m_goTagRoot.transform, nameList, (index) => { OnSwitchTagLogic(index); });
            uniqueIdList.Clear();
            uniqueIdList.AddRange(new List<int>() { 1, 2, 3 });
            SwitchTagGroup.BindUniqueId(uniqueIdList);

            for (var i = 0; i < 3; i++)
            {
                //显示标签图标
                var tagObj = SwitchTagGroup.GetTagObjByIndex(i);
                if (tagObj != null)
                {
                    var icon = tagObj.transform.Find("btn/onBg/icon").GetComponent<UIImage>();
                    icon.SetImage($"Sprite/ui_xianfeng/xianfeng_qieye_icon{i+1}.png");
                }
            }
            InitDayTagList();
            InitTargetTask();
            
            //xiang说明按钮
            ToolScriptExtend.BindBtnLogic(m_btnTargetHelp, 
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 18); });
            
            //说明按钮
            ToolScriptExtend.BindBtnLogic(m_btnTargetHelp, 
                () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 18); });

            
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
            curSelectDay = -1;
            m_goPrefab.SetActive(false);
            //先锋目标   先锋战令   先锋指挥官
            configTasks.Clear();
            configTasks = ChaoZhiManager.GetAllPioneerTaskList();
            
            
            OnSelectDayNodeByIndex(1);
            
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnSwitchTagLogic(int index)
        {
            m_goTargetNode.SetActive(index == 0);
            m_goZLNode.SetActive(index == 1);
            m_goCommanderNode.SetActive(index == 2);

            //显示标签图标
            var tagObj = SwitchTagGroup.GetTagObjByIndex(index);
            if (tagObj != null)
            {
                var icon = tagObj.transform.Find("btn/onBg/icon");
                if (icon != null)
                {
                    icon.DOKill();
                    icon.DOScale(1.1f, 0.2f)
                        .SetEase(Ease.OutBack)
                        .OnComplete(() => {
                            icon.DOScale(1, 0.1f)
                                .SetEase(Ease.InOutQuad);
                        });
                    
                }
            }
            
            // if (index == 0)
            // {
            //     ScrollToRewardIndex();
            // }
        }
        
        #region 按钮事件注册
        private void OnBtnExitClick()
        {
            Close();
        }
        
        //先锋战令 帮助
        private void OnBtnHelpClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 20);
        }
        
        
        private void OnBtnGetAllClick()
        {

        }
        
        private void OnBtnCheckClick()
        {

        }
        
        private void OnBtnBuyClick()
        {

        }
        
        //先锋目标帮助
        private void OnBtnTargetHelpClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 18);
        }
        
        private void OnBtnTargetExtraRewardClick()
        {

        }
        
        private void OnBtnCheck2Click()
        {

        }
        
        #endregion
        
        //实现多语言
        public void CheckMultiLanguage(GameObject obj)
        {
            if (obj == null) return;
            var list = obj.GetComponentsInChildren<MultiLanguage>(true);
            foreach (var item in list)
            {
                var txt = item.gameObject.GetComponent<UIText>();
                if (txt != null)
                {
                    txt.text = ToolScriptExtend.GetLang(item.langId);
                }
            }
        }
        
        #region 第一个标签 先锋目标

        #region 天数节点查看

        private void InitDayTagList()
        {
            dayNodeList.Clear();
            var root = m_goDayNode.transform;
            var txts = root.GetComponentsInChildren<UIText>(true);
            var str = ToolScriptExtend.GetLang(1100485);//“天数”
            foreach (var txt in txts)
            {
                txt.text = str;
            }
            
            for (var i = 1; i <= 5; i++)
            {
                var child = root.Find($"node{i}");
                dayNodeList.Add(new DayNode()
                {
                    day = i,
                    offBg = child.Find("offBg").gameObject,
                    lockBg = child.Find("lockBg").gameObject,
                    onBg = child.Find("onBg").gameObject,
                    lockOnBg = child.Find("lockOnBg").gameObject,
                    btn = child.Find("btn").GetComponent<UIButton>(),
                });
            }
            
            foreach (var node in dayNodeList)
            {
                ToolScriptExtend.BindBtnLogic(node.btn, () =>
                {
                    OnSelectDayNodeByIndex(node.day);
                });
            }
            
        }

        /// <summary>
        /// 控制DayNode显隐
        /// </summary>
        /// <param name="isSelect">是否点击选中</param>
        /// <param name="today">今天是第几天</param>
        /// <param name="node"></param>
        private void SetDayNodeActive(bool isSelect,int today,DayNode node)
        {
            var isUnlock = node.day <= today;
            node.onBg.SetActive(isSelect && isUnlock);
            node.lockOnBg.SetActive(isSelect && !isUnlock);
            node.offBg.SetActive(!isSelect && isUnlock);
            node.lockBg.SetActive(!isSelect && !isUnlock);
        }
        
        //今天是第几天
        private int GetToday()
        {
            long startTimestamp = 1750725027;
            DateTime startDate = DateTimeOffset.FromUnixTimeSeconds(startTimestamp).DateTime;
            DateTime curData = DateTimeOffset.FromUnixTimeSeconds((long)TimeComponent.Now).DateTime;
            TimeSpan duration = curData.Date - startDate.Date;
            return duration.Days + 1;
        }

        private void OnSelectDayNodeByIndex(int day)
        {
            if (curSelectDay == day)
            {
                return;
            }
            curSelectDay = day;
            
            var today = GetToday();
            foreach (var node in dayNodeList)
            {
                SetDayNodeActive(day == node.day, today, node);
            }

            var isUnlock = day <= today;
            m_goTargetTask.SetActive(isUnlock);
            m_goTargetLock.SetActive(!isUnlock);
            if (isUnlock)
            {
                SwitchTargetTaskGroup?.ForceSelect(0);
            }
            else
            {
                ShowDayRewardPre(day);
            }
            
            //活动第{activity_days}天即可解锁目前的先锋任务
            var format = new Dictionary<string, object> { { "activity_days", curSelectDay } };
            m_txtTargetLockTip.text = ToolScriptExtend.GetLangFormat(1100486, format);
        }
        
        
        #endregion

        #region 目标任务

        private void InitTargetTask()
        {
            SwitchTargetTaskGroup = m_goTargetTagGroup.GetComponent<UISwitchTagGroup>();
            var nameList = new List<string>()
            {
                ToolScriptExtend.GetLang(80100032), //"每日登入", 
                ToolScriptExtend.GetLang(1100267), //基地升级
                ToolScriptExtend.GetLang(1100413), //"清理街区"
            };
            var root = SwitchTargetTaskGroup.transform.Find("Viewport/root");
            SwitchTargetTaskGroup.Init(m_goTargetTagItem, root, nameList, (index) =>
            {
                ShowTaskList(curSelectDay, index+1);
                
            });
            SwitchTargetTaskGroup.BindUniqueId(new List<int>() { 1, 2, 3 });
            
            //目标英雄
            var heroId = 11406;
            ToolScriptExtend.ShowHeroSpine(heroId, m_spuiRole);

            //查看英雄跳转
            ToolScriptExtend.BindBtnLogic(m_btnCheck, () =>
            {
                var module = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
                if (module.IsCombind)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroForm);
                }
                else
                {
                    GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroId);
                }
            });

            //完成任务获得：莫妮卡
            m_txtTargetTip.text = ToolScriptExtend.GetLang(1100483);

            var value1 = 0;
            var value2 = 50;

            //解锁下次奖励：
            m_txtTargetProgressTip.text = $"{ToolScriptExtend.GetLang(1100484)}  <color=#ffd800>{value1}/{value2}</color>";
            
        }

        /// <summary>
        /// 获取任务列表 activity_battlepass_task
        /// </summary>
        /// <param name="day">接取任务天数</param>
        /// <param name="index">页签顺序，0：所有页签</param>
        /// <returns></returns>
        private List<activity_battlepass_task> GetTargetTaskListByType(int day,int index)
        {
            var result = new List<activity_battlepass_task>();
            if (configTasks == null) return result;
            var data = configTasks.Where(x =>
            {
                if (index == 0)
                {
                    return x.accept_day == day;
                }
                else
                {
                    return x.accept_day == day && x.page_order == index;
                }
            });
            result.AddRange(data);
            result.Sort((a, b) => a.activity_templateid - b.activity_templateid);
            return result;
        }
        
        private void ShowTaskList(int day,int index)
        {
            if (day < 0) return;
            var list = GetTargetTaskListByType(day, index);
            var sumCount = list.Count;
            var value = taskItemHeight * sumCount + taskSpacing*(sumCount - 1);
            scrollTargetTask.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,value);
            
            ToolScriptExtend.RecycleOrCreate(m_goTaskItem,scrollTargetTaskRoot,sumCount);
            for (var i = 0; i < sumCount; i++)
            {
                var data = list[i];
                var child = scrollTargetTaskRoot.GetChild(i);
                UpdateTaskLogic(i, child.gameObject, data);
            }
        }
        
        private void UpdateTaskLogic(int index, GameObject obj,activity_battlepass_task config)
        {
            if (config == null) return;
            
            var root = obj.transform.Find("bg");
            var name = root.Find("name").GetComponent<UIText>();
            var btnGo = root.Find("btnGo").GetComponent<UIButton>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish");
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var progress = root.Find("progress").GetComponent<UIText>();
            
            var content = ChaoZhiManager.GetTaskFormatStr(config.activity_task_desc,config.task_type,config.task_value);
            var result = $"({1}/{config.task_target_value})";
            var sumStr = content + result;
            name.text = content;
            progress.text = result;
            LayoutRebuilder.ForceRebuildLayoutImmediate(name.rectTransform);
            
            List<reward> rewardList = config.activity_task_reward;
            ToolScriptExtend.RecycleOrCreate(m_goTaskReward, rewardRoot, rewardList.Count);
            
            var rewards = new List<PbGameconfig.reward>();
            foreach (var node in rewardList)
            {
                rewards.Add(new PbGameconfig.reward() { ItemId = (PbGameconfig.itemid)node.item_id, Num = node.num });
            }
            
            ChaoZhiManager.ShowRewardList(m_goReward, rewardRoot, rewards, 0.56f);
            scroll.enabled = rewardList.Count > 4;
            
            //奖励领取状态0：进行中 1：可领取  2：已领取
            // var status = ChaoZhiManager.GetZLTaskStatus(data);
            var status = 0;
            btnGo.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            
            ToolScriptExtend.BindBtnLogic(btnGo, () => { });
            ToolScriptExtend.BindBtnLogic(btnGet, () =>
            {
                // var req = new ActivityBattlePassDrawTaskReq
                // {
                //     Template = MsgData.Template,
                //     LoopTimes = MsgData.LoopTimes,
                //     TaskId = data.Id
                // };
                // ChaoZhiManager.C2SActivityBattlePassDrawTaskReq(req, (resp) =>
                // {
                //     var iconId = ChaoZhiManager.GetZLScoreIcon(TemplateId);
                //     var temp = config.activity_task_reward.FirstOrDefault(x => x.item_id == iconId);
                //     if (temp != null)
                //     {
                //         FlyResManager.UIFlyByTrans(iconId, (int)temp.num, btnGet.transform, m_imgScore.transform);
                //     }
                // });
            });
        }

        //展示指定天数预览奖励
        private void ShowDayRewardPre(int day) 
        {
            var list = GetTargetTaskListByType(day, 0);

            var dic = new Dictionary<itemid, long>();
            foreach (var node in list)
            {
                foreach (var rewards in node.activity_task_reward)
                {
                    if (!dic.ContainsKey(rewards.item_id))
                    {
                        dic[rewards.item_id] = 0;

                    }
                    dic[rewards.item_id] += rewards.num;
                }
            }

            List<reward> rewardList = new List<reward>();
            foreach (var data in dic)
            {
                if(ToolScriptExtend.GetItemConfig(data.Key) == null)continue;
                rewardList.Add(new reward{item_id = data.Key,num = data.Value});
            }

            rewardList.Sort(ChaoZhiManager.CompareRewardLogic);
            
            var root = m_scrollviewPreview.content.transform;
            ToolScriptExtend.RecycleOrCreate(m_goTaskReward,root,rewardList.Count);
            for (var j = 0; j < rewardList.Count; j++)
            {
                var info = rewardList[j];
                var rewardChild = root.GetChild(j);
                var node = rewardChild.Find("node");
                node.localScale = Vector3.one * 0.5f;
                if (node.childCount == 0)
                {
                    BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
                    {
                        module.SetClick(module.OpenTips);
                        ToolScriptExtend.SetItemObjTxtScale(module.gameObject, 1.3f);
                    });
                }
                else
                {
                    ToolScriptExtend.SetItemObjInfo(node, node.GetChild(0).gameObject, info.item_id, (int)info.num);
                }
            }
            
        }


        #region 进度条

        private void SetScoreNode()
        {
            
            
            
            
        }
         
        
        

        #endregion
        
        
        
        
        
        
        
        #endregion
        
        
        
        

        #endregion
        
        #region 第二个标签 先锋战令

        

        #endregion
        
        
        #region 第二个标签 先锋指挥官

        

        #endregion
        
    }
}
