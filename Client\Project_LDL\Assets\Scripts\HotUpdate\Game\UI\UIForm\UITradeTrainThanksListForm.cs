using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UITradeTrainThanksListForm : UGuiFormEx
    {
        List<Trade.TradeTrainThanks> thankList = new(); 
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Trade.TradeTrainThanksListResp data)
            {
                thankList = new(data.ThanksList);
            }

            m_goPlayerItem.SetActive(false);
            RefreshPanel();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        void InitPanel()
        {
            m_TableViewV.GetItemCount = () =>
            {
                return thankList.Count;
            };
            m_TableViewV.GetItemGo = () => m_goPlayerItem;
            m_TableViewV.UpdateItemCell = UpdatePlayerView;
            m_TableViewV.InitTableViewByIndex(0, 12);
        }

        void UpdatePlayerView(int index, GameObject obj)
        {
            UIText txtDesc = obj.transform.Find("bg/txtDesc").GetComponent<UIText>();
            UIText txtTime = obj.transform.Find("bg/txtTime").GetComponent<UIText>();
            GameObject imgContract = obj.transform.Find("bg/imgContract").gameObject;

            trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
            if (setting != null)
            {
                int random = Random.Range(0, setting.thank_lang.Count);
                uint langID = setting.thank_lang[random];
                txtDesc.text = ToolScriptExtend.GetLang((int)langID);
                ToolScriptExtend.AdjustLineSpacing(txtDesc);
            }

            Trade.TradeTrainThanks tradePassenger = thankList[index];

            txtTime.text = TimeHelper.FormatToMonthDayTime((ulong)tradePassenger.ThanksTime);

            if (tradePassenger.Article != null && tradePassenger.Article.Count > 0)
            {
                imgContract.SetActive(true);
                if (imgContract.transform.childCount > 0)
                {
                    UIItemModule uiItemModule = imgContract.transform.GetChild(0).GetComponent<UIItemModule>();
                    if (uiItemModule != null)
                    {
                        uiItemModule.SetData(itemid.itemid_1010071, tradePassenger.Article[0].Amount);
                        uiItemModule.InitConfigData();
                        uiItemModule.DisplayInfo();
                    }
                }
                else
                {
                    BagManager.CreatItem(imgContract.transform, itemid.itemid_1010071, tradePassenger.Article[0].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.transform.Find("count").localScale = new Vector2(1.6f, 1.6f);
                    });
                }
            }
            else
            {
                imgContract.SetActive(false);
            }

            GameEntry.RoleData.RequestRoleQueryLocalSingle(tradePassenger.RoleId, (roleBrief) =>
            {
                ColorLog.Pink("查询乘客信息", roleBrief);
                if (roleBrief != null)
                {
                    UIText txtName = obj.transform.Find("bg/txtName").GetComponent<UIText>();
                    txtName.text = roleBrief.Name;
                }
            });
        }

        void RefreshPanel()
        {
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0, 12);
            }
            else
            {
                m_TableViewV.ReloadData();
            }

            m_goNoData.SetActive(thankList.Count == 0);

            RefreshContractCount();
        }

        void RefreshContractCount()
        {
            long count = 0;
            bool hasReward = false;

            for (int i = 0; i < thankList.Count; i++)
            {
                if (thankList[i].Article != null && thankList[i].Article.Count > 0)
                {
                    count += thankList[i].Article[0].Amount;

                    if (!thankList[i].Status)
                    {
                        hasReward = true;
                    }
                }
            }

            if (hasReward)
            {
                if (GameEntry.TradeTruckData.myTrain != null)
                {
                    long trainID = GameEntry.TradeTruckData.myTrain.Id;
                    GameEntry.TradeTruckData.RequestTrainReceiveThanksReward(trainID, (result) =>
                    {
                        ColorLog.Pink("领取感谢奖励", result);
                        if (result != null)
                        {
                            List<reward> rewards = new();
                            foreach (var item in result.Article)
                            {
                                rewards.Add(new reward()
                                {
                                    item_id = (itemid)item.Code,
                                    num = item.Amount
                                });
                            }
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                        }
                    });
                }
            }

            m_txtContractCount.text = $"x{count}";
        }
    }
}
