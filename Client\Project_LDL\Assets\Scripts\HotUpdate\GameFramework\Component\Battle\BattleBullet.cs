using System;
using System.Collections.Generic;
using EasingCore;
using Game.Hotfix.Config;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Game.Hotfix
{
    public abstract class BattleBullet : BattleUnitBase
    {
        public bool IsDie => m_IsDie;
        public float AlivePct => m_AlivePct;
        public skill_show SkillShowCfg => m_Config;

        protected BattleFiled m_BattleFiled;
        protected skill_show m_Config;
        protected List<SkillTargetData> m_TargetList;
        protected bool m_IsFake;

        private bool m_IsDie = false;
        private float m_Duration = 0;
        private bool m_IsAllHurt;
        private target_type m_TargetType;

        private BattleEffect m_BulletTrailEffect;
        private float m_BulletTrailRemoveDelay;
        
        private float m_AliveTime;
        private float m_AlivePct;

        
        
        
        public void Init(BattleFiled battleFiled, skill_show config, List<SkillTargetData> targetList, bool isFake)
        {
            m_BattleFiled = battleFiled;
            m_Config = config;
            m_TargetList = targetList;
            m_IsFake = isFake;

            m_IsDie = false;
            m_Duration = m_Config.b_end_time - m_Config.b_start_time;//子弹的持续时间等于:子弹开始时间到技能结束
            
            //创建拖尾
            m_BulletTrailEffect = null;
            if (m_Config.b_trailing_id > 0)
            {
                m_BulletTrailRemoveDelay = m_Config.b_trailing_time;
                m_BulletTrailEffect = m_BattleFiled.EffectCtrl.GetEffect(m_Config.b_trailing_id,null,GetPosition(),GetRotation());
            }

            m_AlivePct = 0;
            m_AliveTime = 0;
            
            Load();

            OnInit();
        }

        protected abstract void OnInit();
        protected abstract void OnBulletTick(float dt);
        protected abstract void OnUnInit();
        
        public SkillTargetData GetFirstTarget()
        {
            if (m_TargetList != null && m_TargetList.Count >= 0)
            {
                return m_TargetList[0];
            }

            return null;
        }

        protected override void OnTick(float dt)
        {
            m_AliveTime += dt;
            if (m_AliveTime > m_Duration)
                m_AliveTime = m_Duration;
            if (m_Duration > 0)
                m_AlivePct = TimeEaseProcess(m_AliveTime / m_Duration, m_Config.b_time_line_type);
            else
                m_AlivePct = 1;

            OnBulletTick(dt);
        }

        protected override Type GetEntityLogicType()
        {
            return typeof(EL_BattleBullet);
        }

        protected override string GetPrefabPath()
        {
            if (m_Config.b_effect_id != 0)
            {
                var config = GameEntry.LDLTable.GetTableById<battle_effect>(m_Config.b_effect_id);
                return config.res_location;    
            }

            return string.Empty;
        }

        protected override ED_BattleUnitBase GetBattleUnitData()
        {
            ED_BattleBullet data =
                new ED_BattleBullet(Game.GameEntry.Entity.GenerateSerialId());
            data.Position = Position;
            data.Rotation = Rotation;
            data.Scale = Scale;
            return data;
        }

        protected Vector3 GetRandomBombRange()
        {
            float r = m_Config.b_bomb_range;
            float x = m_Config.b_bomb_range_x;
            float y = m_Config.b_bomb_range_y;
            if(r == 0 && x == 0 && y == 0)
                return Vector3.zero;

            return BattleUtils.GetRandomPointInGroundCircle(r + x, r + y);
        }
        
        protected override void UnInit()
        {
            if (m_BulletTrailEffect != null)
            {
                if (m_BulletTrailRemoveDelay > 0)
                    m_BattleFiled.EffectCtrl.RemoveEffectDelay(m_BulletTrailEffect, m_BulletTrailRemoveDelay);
                else
                    m_BattleFiled.EffectCtrl.RemoveEffect(m_BulletTrailEffect);
                m_BulletTrailEffect = null;
            }
            
            OnUnInit();
        }

        protected override void OnLoaded(Entity entity)
        {
        }

        public void PlayBombEffect(Vector3? position)
        {
            if (position == null)
                position = Position;
            var hitEffectId = m_Config.b_hurt_effect_id;
            if (hitEffectId <= 0)
                return;
            var effect = m_BattleFiled.EffectCtrl.GetEffect(hitEffectId);
            effect.SetPosition(position.Value);
        }
        
        public void PlayBombEffectAuto()
        {
            if (m_Config.b_hurt_effect_all)
            {
                //给每个人 播放一个特效
                foreach (var target in m_TargetList)
                {
                    if (target != null)
                    {
                        PlayBombEffect(null);
                        DoFlash(target);
                    }
                }
            }
            else
            {
                PlayBombEffect(null);
            }
        }

        private void DoFlash(SkillTargetData targetData)
        {
            if (targetData != null && targetData.Target is BattleHero hero)
            {
                hero.DoFlash();
            }
        } 

        private float TimeEaseProcess(float pct, battle_time_ease eType)
        {
            if (pct == 0 || Mathf.Approximately(pct, 1))
                return pct;
            if (eType == battle_time_ease.none)
                return pct;
            if (eType == battle_time_ease.in_sine)
                return Easing.Get(Ease.InSine).Invoke(pct);
            if (eType == battle_time_ease.out_sine)
                return Easing.Get(Ease.OutSine).Invoke(pct);
            if (eType == battle_time_ease.in_out_sine)
                return Easing.Get(Ease.InOutSine).Invoke(pct);

            if (eType == battle_time_ease.in_quad)
                return Easing.Get(Ease.InQuad).Invoke(pct);
            if (eType == battle_time_ease.out_quad)
                return Easing.Get(Ease.OutQuad).Invoke(pct);
            if (eType == battle_time_ease.in_out_quad)
                return Easing.Get(Ease.InOutQuad).Invoke(pct);

            if (eType == battle_time_ease.in_cubic)
                return Easing.Get(Ease.InCubic).Invoke(pct);
            if (eType == battle_time_ease.out_cubic)
                return Easing.Get(Ease.OutCubic).Invoke(pct);
            if (eType == battle_time_ease.in_out_cubic)
                return Easing.Get(Ease.InOutCubic).Invoke(pct);

            if (eType == battle_time_ease.in_quart)
                return Easing.Get(Ease.InQuart).Invoke(pct);
            if (eType == battle_time_ease.out_quart)
                return Easing.Get(Ease.OutQuart).Invoke(pct);
            if (eType == battle_time_ease.in_out_quart)
                return Easing.Get(Ease.InOutQuart).Invoke(pct);

            // if (eType == BATTLE_TIME_EASE.Bezier)
            // {
            //     return GetCubicBezierPos(pct,);
            // }

            return pct;
        }

        public void SetBulletRotation(Vector3 to,Vector3 from)
        {
            var direction = to - from;
            var rotation = Quaternion.LookRotation(direction.normalized);
            SetRotation(rotation);
        }

        public void SetBulletPosition(Vector3 position)
        {
            if (m_BulletTrailEffect != null)
            {
                m_BulletTrailEffect.SetPosition(position);
            }
            
            SetPosition(position);
        }
        
    }
}