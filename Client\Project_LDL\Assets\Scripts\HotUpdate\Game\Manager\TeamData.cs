using System;
using System.Collections.Generic;
using System.Linq;
using Fight;
using Google.Protobuf.Collections;
using Roledata;
using Team;
using UnityEngine;

namespace Game.Hotfix
{
    public class TeamData
    {
        private Dictionary<TeamType, List<FormationHero>> m_Teams;
        public List<DefendTeam> defendTeamList;

        public void Init(RoleTeam roleTeam)
        {
            m_Teams = new Dictionary<TeamType, List<FormationHero>>();
            defendTeamList = new();

            if (roleTeam == null)
                return;

            foreach (var team in roleTeam.Teams)
            {
                if (m_Teams.TryAdd(team.TeamType, team.Heroes.ToList()))
                {

                }
            }
            GameEntry.LogicData.HeroData.SetHeroTeamId();

            defendTeamList = roleTeam.DefendTeams.ToList();
            InitEvent();
        }

        private void InitEvent()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushFormationTeam, message =>
            {
                var data = (PushFormationTeam)message;
                OnFormationTeamChange(data.Teams);
            });
        }

        private void OnFormationTeamChange(RepeatedField<FormationTeam> formationTeams)
        {
            foreach (var team in formationTeams)
            {
                List<FormationHero> newList = team.Heroes.ToList();
                if (m_Teams.TryGetValue(team.TeamType, out var oldList))
                {
                    bool isEqual = oldList.SequenceEqual(newList);
                    if (!isEqual)
                    {
                        GameEntry.Event.Fire(TeamChangeEventArgs.EventId, TeamChangeEventArgs.Create(team.TeamType));
                    }
                }
                m_Teams[team.TeamType] = newList;
                GameEntry.LogicData.HeroData.SetHeroTeamId();
            }
        }

        public List<FormationHero> GetTeam(TeamType teamType)
        {
            if (m_Teams.TryGetValue(teamType, out var team))
            {
                return team;
            }

            return null;
        }
        
        public Dictionary<EnumBattlePos,int> GetTeamDic(TeamType teamType)
        {
            Dictionary<EnumBattlePos,int> dic = new Dictionary<EnumBattlePos, int>();
            
            if (m_Teams.TryGetValue(teamType, out var team))
            {
                if (team == null)
                {
                    return null;
                }
                foreach (var hero in team)
                {
                    if (hero != null)
                    {

                        dic.TryAdd((EnumBattlePos)hero.Pos, (int)hero.HeroId);
                        
                    }
                }
            }
            return dic;
        }

        public bool IsTeamTypeUnlock(TeamType teamType)
        {
            if (teamType == TeamType.Common3 || teamType == TeamType.Common4)
                return false;
            return true;
        }
        
        public void TeamModify(FormationTeam formationTeam, Action callback)
        {
            var list = new List<FormationTeam>();
            list.Add(formationTeam);
            TeamModify(list, callback);
        }

        public void TeamModify(List<FormationTeam> formationTeams, Action callback)
        {
            TeamModifyReq req = new TeamModifyReq();
            
            req.Teams.AddRange(formationTeams);
            
            GameEntry.LDLNet.Send(Protocol.MessageID.TeamModify, req, (message) =>
            {
                if (message is TeamModifyResp)
                {
                    foreach (var team in formationTeams)
                    {
                        List<FormationHero> newList = team.Heroes.ToList();
                        if (m_Teams.TryGetValue(team.TeamType, out var oldList))
                        {
                            bool isEqual = oldList.SequenceEqual(newList);
                            if (!isEqual)
                            {
                                GameEntry.Event.Fire(TeamChangeEventArgs.EventId, TeamChangeEventArgs.Create(team.TeamType));
                            }
                        }
                        m_Teams[team.TeamType] = newList;
                        GameEntry.LogicData.HeroData.SetHeroTeamId();
                    }
                    callback?.Invoke();
                }
            });
        }

        /// <summary>
        /// 防守队伍配置
        /// </summary>
        /// <param name="teams"></param>
        /// <param name="callback"></param>
        public void TeamDefendModify(List<DefendTeam> teams, Action callback)
        {
            var req = new TeamDefendModifyReq();
            req.Teams.AddRange(teams);

            GameEntry.LDLNet.Send(Protocol.MessageID.TeamDefendModify, req, (message) =>
            {
                defendTeamList = teams;
                callback?.Invoke();
            });
        }
    }
}
