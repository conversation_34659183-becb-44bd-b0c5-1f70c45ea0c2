using UnityEngine;

namespace Game.Hotfix
{
    public class HUDTech : HUDItemTickAble
    {
        [SerializeField] private UIButton m_btnDisplayRoot;
        [SerializeField] private UIImage m_imgWait;
        [SerializeField] private UIImage m_imgFull;
        [SerializeField] private UIImage m_imgNormalIcon;
        [SerializeField] private UIImage m_imgWorkIcon;
        [SerializeField] private UIText m_txtNumber;
        [SerializeField] private Animation m_Animation;
        
        
        private EL_Building m_Building;
        private BuildingModule m_BuildingModule;
        private float m_TimeElapsed = 0;
        private float m_TimeInterval = 1;
        private TechQueue m_QueueModule;
        private bool isFinish = false;
        private bool isWait = false;
        void Start()
        {
            m_btnDisplayRoot.onClick.AddListener(OnBtnTechClick);
        }
        
        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Building = Owner as EL_Building;
            m_BuildingModule = m_Building?.GetBuildingModule();
            if (m_BuildingModule == null)
            {
                return;
            }
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnTechChange,OnTechChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnResetPosition,OnResetPosition);
            m_Building.AddEventListener(EL_BuildingEvent.OnMenuVisibleChange, OnMenuVisibleChange);
            
            ResetUI();
        }

        private void OnTechChange(object obj)
        {
            if (obj is TechChangState state)
            {
                if (state == TechChangState.Remove)
                {
                    GameEntry.HUD.HideHUD(this);
                }
                else
                {
                    ResetUI();
                }
            }
        }

        protected void ResetUI()
        {
            m_QueueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)m_BuildingModule.BuildingId) as TechQueue;
            bool isWorking = m_QueueModule != null;
            isWait = m_QueueModule == null;
            if (isWait)
            {
                isFinish = false;
                m_imgNormalIcon.gameObject.SetActive(true);
                m_txtNumber.gameObject.SetActive(false);
                m_imgFull.gameObject.SetActive(false);
                m_imgWorkIcon.gameObject.SetActive(false);
            }
            else if (isWorking)
            {
                m_imgWorkIcon.gameObject.SetActive(true);
                m_imgNormalIcon.gameObject.SetActive(false);
                isFinish = m_QueueModule.IsFinish();
                m_imgFull.gameObject.SetActive(isFinish);
                m_txtNumber.gameObject.SetActive(!isFinish);
                int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup((int)m_QueueModule.curTechGroup);
                var config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel((int)m_QueueModule.curTechGroup, curLevel);
                if (config != null && !string.IsNullOrEmpty(config.tech_icon))
                {
                    m_imgWorkIcon.SetImage(config.tech_icon);
                }
            }
        }
        
        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            m_TimeElapsed += dt;
            if (m_TimeElapsed > m_TimeInterval)
            {
                ResetTimeUI();
                m_TimeElapsed -= m_TimeInterval;
            }
        }

        protected void ResetTimeUI()
        {
            if (m_QueueModule != null && !isFinish)
            {
                var remainTime = m_QueueModule.GetRemainTime();
                m_txtNumber.text = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(remainTime));
            }
        }
        
        protected override Vector3 GetOffset()
        {
            if (m_BuildingModule != null)
            {
                var offset = m_BuildingModule.GetOffsetCenter();
                return new Vector3(offset.x, 4.7f, offset.y);
            }

            return base.GetOffset();
        }

        private void OnMenuVisibleChange(object obj)
        {
            if (obj is bool show)
                gameObject.SetActive(!show);
        }

        private void OnResetPosition(object obj)
        {
            Refresh(Owner.transform);
        }
        
        protected override void OnUnInit()
        {
            base.OnUnInit();
            if (m_BuildingModule != null)
            {
                m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnTechChange, OnTechChange);
                m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnResetPosition, OnResetPosition);
            }
            if (m_Building != null)
            {
                m_Building.RemoveEventListener(EL_BuildingEvent.OnMenuVisibleChange, OnMenuVisibleChange);
            }
        }

        private void OnBtnTechClick()
        {
            if (isFinish)
            {
                GameEntry.LogicData.TechData.CompeleteTech(m_QueueModule);
                return;
            }
            if (m_QueueModule == null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechForm, m_BuildingModule);
                return;
            }
            TechDetail param = new TechDetail();
            param.techGroup = (int)m_QueueModule.curTechGroup;
            param._curBuildingModule = m_BuildingModule;
            GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
        }
    }
}