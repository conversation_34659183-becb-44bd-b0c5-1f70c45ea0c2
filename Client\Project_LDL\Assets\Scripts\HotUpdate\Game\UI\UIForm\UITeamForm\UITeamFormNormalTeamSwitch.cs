using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class UITeamFormNormalTeamSwitch : MonoBehaviour
    {
        public ToggleGroup toggleGroup;
        public List<UIToggle> toggleList;
        public UIToggle curToggle;

        private UITeamForm m_UiTeamForm;
        public void OnOpen(UITeamForm uiTeamForm)
        {   
            m_UiTeamForm = uiTeamForm;
            
            for (int i = 0; i < toggleList.Count; i++)
            {
                var index = i;
                var toggle = toggleList[i];
                Transform imgLock = toggle.transform.Find("imgLock");
                bool teamIsUnlock = GameEntry.LogicData.BuildingData.GetTeamIsUnlock(index + 1);
                imgLock.gameObject.SetActive(!teamIsUnlock);

                

                if (toggle.isOn)
                {
                    curToggle = toggle;
                }
                toggle.onValueChanged.RemoveAllListeners();
                toggle.onValueChanged.AddListener((value) =>
                {
                    if (!teamIsUnlock)
                    {
                        if (curToggle != null)
                        {
                            curToggle.isOn = true;
                        }

                        if (index != toggleList.Count - 1)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = ToolScriptExtend.GetLang(1333),
                            });
                        }
                        else
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIMallForm,paymenttype.paymenttype_monthycard);
                        }

                        return;
                    }

                    // 检查编队是否被占用（仅对贸易货车防守编队）
                    if (value && m_UiTeamForm != null && m_UiTeamForm.CurTeamFormType == UITeamFormType.TradeTruckDefend)
                    {
                        var teamType = m_UiTeamForm.TeamTypes[index];
                        var occupiedTeams = GameEntry.TradeTruckData.GetOccupiedTradeVanDefendTeams();
                        if (occupiedTeams.Contains(teamType))
                        {
                            // 恢复之前的选择
                            if (curToggle != null)
                            {
                                curToggle.isOn = true;
                            }

                            // 显示编队被占用的飘字提示
                            string teamName = ToolScriptExtend.GetLang(1009) + (index + 1);
                            string message = $"{teamName}正在执行任务中，无法切换";
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                            {
                                Content = message,
                            });
                            return;
                        }
                    }

                    if (value)
                    {
                        curToggle = toggle;
                        m_UiTeamForm?.SwitchTeamIndex(index);
                    }

                });
            }
        }
        
        public void OnClose()
        {
            
            for (int i = 0; i < toggleList.Count; i++)
            {
                var toggle = toggleList[i];
                toggle.onValueChanged.RemoveAllListeners();
            }
        }

        public void SwitchTo(int index, bool? force = null)
        {
            if (index < toggleList.Count)
            {
                toggleList[index].isOn = true;
            }
        }
    }
}
