using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using System;
using GameFramework.Event;
using UnityEngine.Rendering.Universal;
using System.Runtime.InteropServices.WindowsRuntime;

namespace Game.Hotfix
{
    public class TechDetail
    {
        public int techGroup;
        public BuildingModule _curBuildingModule;
    }
    public partial class UITechForm : UGuiFormEx
    {

        private List<GameObject> m_techItems = new List<GameObject>();
        private Dictionary<int, TechItemData> m_techDataDict = new Dictionary<int, TechItemData>();
        public Dictionary<int, TechDetailParamsModel> curShowPanelList = new Dictionary<int, TechDetailParamsModel>();
        public BuildingModule CurBuildingModule;
        public TechSingleModel curClickSingle;
        public List<GameObject> m_goLineList; //= new List<GameObject>();
        public TechQueue queueModule;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            m_goLineList = new List<GameObject> { m_goLine1, m_goLine2, m_goLine3 };
            // m_goLineList.Add(m_goLine1);
            // m_goLineList.Add(m_goLine2);
            // m_goLineList.Add(m_goLine3);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            CurBuildingModule = userData as BuildingModule;
            queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)CurBuildingModule.BuildingId) as TechQueue;

            // 初始化科技数据
            InitTechData();

            // 生成科技项
            GenerateTechItems();
            //刷新一层研究状态
            OnRefreshTabState();

            //刷新Under
            OnRefreshUnderState();
            //刷新礼包
            OnRefreshGift();
            GameEntry.Event.Subscribe(TechChangeEventArgs.EventId, OnTechChangeUpdate);
            GameEntry.Event.Subscribe(PrivilegeChangeEventArgs.EventId, OnPrivilegeChange);
        }

        public void OnRefreshGift()
        {
            m_goGift.SetActive(false);
            if (!GameEntry.LogicData.TechData.IsBuyLine2() && GameEntry.LogicData.TechData.IsCanBuyLine2())
            {
                m_txtGiftDes.text = ToolScriptExtend.GetLang(1100477);
                payment payment = GameEntry.LDLTable.GetTableById<payment>((int)paymentid.paymentid_20201001);
                if (payment != null)
                {
                    m_txtGiftPrice.text = ToolScriptExtend.GetLang(payment.price_lang_id);
                }
                m_goGift.SetActive(true);
                return;
            }
            if (!GameEntry.LogicData.TechData.IsBuyLine3() && GameEntry.LogicData.TechData.IsCanBuyLine3())
            {
                m_txtGiftDes.text = ToolScriptExtend.GetLang(1100478);
                payment payment = GameEntry.LDLTable.GetTableById<payment>((int)paymentid.paymentid_20301002);
                if (payment != null)
                {
                    m_txtGiftPrice.text = ToolScriptExtend.GetLang(payment.price_lang_id);
                }
                m_goGift.SetActive(true);
                return;
            }
            
        }
        void OnTechChangeUpdate(object sender, GameEventArgs e)
        {
            queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)CurBuildingModule.BuildingId) as TechQueue;
            ChangeOnRefresh();
        }
        void OnPrivilegeChange(object sender, GameEventArgs e)
        {
            OnRefreshGift();
            OnRefreshUnderState();
        }
        public void ChangeOnRefresh()
        {
            //刷新第一层研究状态
            OnRefreshTabState();
            // 更新科技项显示百分比
            for (int i = 0; i < m_techItems.Count; i++)
            {
                var techData = m_techDataDict.Values.ElementAt(i);
                UpdateTechItem(m_techItems[i], techData);
            }

            //刷新礼包
            OnRefreshGift();
            //刷新第二层
            OnRefreshTab2State();
            //刷新Under
            OnRefreshUnderState();
            OnRefreshLine();
        }
        //刷新一层研究状态
        public void OnRefreshTabState()
        {
            for (int i = 0; i < m_techItems.Count; i++)
            {
                Transform lienList = m_techItems[i].transform.Find("lineList");
                Transform eff = m_techItems[i].transform.Find("eff");
                for (int k = 0; k < lienList.childCount; k++)
                {
                    lienList.GetChild(k).gameObject.SetActive(false);
                }
                eff.gameObject.SetActive(false);
            }

            TechQueue queue1 = GameEntry.LogicData.TechData.GetTechQueueLine1();
            if (queue1 != null && queue1.curTechGroup != 0)
            {
                int tabIndex = GameEntry.LogicData.TechData.GetTabIndexByGroup(queue1.curTechGroup);
                var parent = m_techItems[tabIndex - 1].transform.Find("lineList").gameObject;
                var child = parent.transform.GetChild(0);
                child.gameObject.SetActive(true);
                
                if(queue1.IsFinish())
                {
                    var eff = m_techItems[tabIndex - 1].transform.Find("eff").gameObject;
                    eff.SetActive(true);
                }
            }
            TechQueue queue2 = GameEntry.LogicData.TechData.GetTechQueueLine2();
            if (queue2 != null && queue2.curTechGroup != 0)
            {
                int tabIndex = GameEntry.LogicData.TechData.GetTabIndexByGroup(queue2.curTechGroup);
                var parent = m_techItems[tabIndex - 1].transform.Find("lineList").gameObject;
                var child = parent.transform.GetChild(1);
                child.gameObject.SetActive(true);
                
                if(queue2.IsFinish())
                {
                    var eff = m_techItems[tabIndex - 1].transform.Find("eff").gameObject;
                    eff.SetActive(true);
                }
            }
            TechQueue queue3 = GameEntry.LogicData.TechData.GetTechQueueLine3();
            if (queue3 != null && queue3.curTechGroup != 0)
            {
                int tabIndex = GameEntry.LogicData.TechData.GetTabIndexByGroup(queue3.curTechGroup);
                var parent = m_techItems[tabIndex - 1].transform.Find("lineList").gameObject;
                var child = parent.transform.GetChild(2);
                child.gameObject.SetActive(true);
                
                if(queue3.IsFinish())
                {
                    var eff = m_techItems[tabIndex - 1].transform.Find("eff").gameObject;
                    eff.SetActive(true);
                }
            }
        }
        //刷新二层状态
        public void OnRefreshTab2State()
        {
            foreach (var item in curShowPanelList)
            {
                if (item.Value.leftItem != null)
                {
                    GameObject leftObj = item.Value.obj.transform.Find("itemLeft").gameObject;
                    item.Value.leftItem.OnRefresh2(leftObj);
                }
                if (item.Value.midItem != null)
                {
                    GameObject midObj = item.Value.obj.transform.Find("itemMid").gameObject;
                    item.Value.midItem.OnRefresh2(midObj);
                }
                if (item.Value.rightItem != null)
                {
                    GameObject rightObj = item.Value.obj.transform.Find("itemRight").gameObject;
                    item.Value.rightItem.OnRefresh2(rightObj);
                }
            }
        }
        //刷新Under
        public void OnRefreshUnderState()
        {
            int index = 1;
            foreach (var item in m_goLineList)
            {
                //队列1.
                TechQueue queue = null;
                UIText txtTime;
                Image icon = item.transform.Find("icon").GetComponent<Image>();
                UIText progress = item.transform.Find("progress").GetComponent<UIText>();
                GameObject btn = m_btnQueue1.gameObject; //= item.transform.Find($"m_btnQueue{index}").gameObject;
                UIButton btnAdd = item.transform.Find($"m_goSlider{index}/btnAdd").GetComponent<UIButton>();
                UIButton btnHelp = item.transform.Find($"m_goSlider{index}/btnHelp").GetComponent<UIButton>();
                UIText txtBtn = item.transform.Find("Text").GetComponent<UIText>();
                GameObject goSlider = item.transform.Find($"m_goSlider{index}").gameObject;
                Slider slider = m_sliderLine1;
                switch (index)
                {
                    case 1:
                        txtTime = m_txtSliderLine1;
                        slider = m_sliderLine1;
                        btn = m_btnQueue1.gameObject;
                        queue = GameEntry.LogicData.TechData.GetTechQueueLine1();
                        break;
                    case 2:
                        txtTime = m_txtSliderLine2;
                        queue = GameEntry.LogicData.TechData.GetTechQueueLine2();
                        slider = m_sliderLine2;
                        btn = m_btnQueue2.gameObject;
                        break;
                    case 3:
                        slider = m_sliderLine3;
                        queue = GameEntry.LogicData.TechData.GetTechQueueLine3();
                        txtTime = m_txtSliderLine3;
                        btn = m_btnQueue3.gameObject;
                        break;
                    default:
                        txtTime = m_txtSliderLine1;
                        break;
                }

                progress.text = "";
                icon.gameObject.SetActive(false);
                btn.SetActive(false);
                goSlider.SetActive(false);
                if (queue != null)
                {
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup((int)queue.curTechGroup);
                    var config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel((int)queue.curTechGroup, curLevel);
                    if (config != null)
                    {
                        icon.gameObject.SetActive(true);
                        icon.SetImage(config.tech_icon);
                        int level = Math.Min(curLevel, config.tech_lv_limit);
                        progress.text = $"{level}/{config.tech_lv_limit}";
                    }
                    if (queue.IsFinish())
                    {
                        btn.SetActive(true);
                        txtBtn.text = ToolScriptExtend.GetLang(1100130);
                        goSlider.SetActive(false);
                        txtTime.text = "";

                    }
                    else
                    {
                        // 创建定时器，每秒更新倒计时
                        btn.SetActive(false);
                        txtBtn.text = "";
                        goSlider.SetActive(true);
                        float currentRemaining = queue.GetRemainTime();
                        if (currentRemaining <= 0)
                        {
                            btn.SetActive(true);
                            txtBtn.text = ToolScriptExtend.GetLang(1100130);
                            txtTime.text = txtTime.text = ToolScriptExtend.GetLang(1100130);
                            goSlider.SetActive(false);
                            slider.value = 0f;
                        }
                        else
                        {
                            txtTime.text = TimeHelper.FormatGameTimeWithDays((int)currentRemaining);
                            slider.value = 1 - (currentRemaining / queue.GetTotalTime());
                        }
                        Timers.Instance.Add("OnRefreshUnderState", 1f, (param) =>
                        {
                            if (txtTime != null)
                            {
                                float currentRemaining = queue.GetRemainTime();
                                if (currentRemaining <= 0)
                                {
                                    btn.SetActive(true);
                                    txtBtn.text = ToolScriptExtend.GetLang(1100130);
                                    txtTime.text = txtTime.text = ToolScriptExtend.GetLang(1100130);
                                    Timers.Instance.Remove("OnRefreshUnderState");
                                    goSlider.SetActive(false);
                                    slider.value = 0f;
                                    //queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)CurBuildingModule.BuildingId) as TechQueue;
                                }
                                else
                                {
                                    txtTime.text = TimeHelper.FormatGameTimeWithDays((int)currentRemaining);
                                    slider.value = 1 - (currentRemaining / queue.GetTotalTime());
                                }
                            }
                        }, -1); // -1表示无限循环 
                    }

                }
                else
                {
                    btn.SetActive(false);
                    txtBtn.text = ToolScriptExtend.GetLang(1100133);
                }
                if (index == 2 || index == 3)
                {
                    Transform isLock = item.transform.Find("isLock");
                    if (index == 2)
                    {
                        isLock.gameObject.SetActive(!GameEntry.LogicData.TechData.IsBuyLine2());
                        item.SetActive(GameEntry.LogicData.TechData.IsCanBuyLine2());
                    }
                    if (index == 3)
                    {
                        isLock.gameObject.SetActive(!GameEntry.LogicData.TechData.IsBuyLine3());
                        item.SetActive(GameEntry.LogicData.TechData.IsCanBuyLine3());
                    }
                    if (!GameEntry.LogicData.TechData.IsBuyLine2() || !GameEntry.LogicData.TechData.IsBuyLine3())
                    {
                        UIButton lockLine = item.transform.Find("isLock/lockLine").GetComponent<UIButton>();
                        lockLine.onClick.RemoveAllListeners();
                        lockLine.onClick.AddListener(() =>
                        {
                            OnBtnGiftClick();
                        });
                    }
                    
                }
                int _index = index;
                btnAdd.onClick.RemoveAllListeners();
                btnAdd.onClick.AddListener(() =>
                {
                    OnBtnSpeedUpClick(_index);
                });

                var isCanHelp = queue != null && queue.Help == 0 && GameEntry.LogicData.UnionData.IsJoinUnion();
                btnHelp.onClick.RemoveAllListeners();
                btnHelp.onClick.AddListener(() =>
                {
                    if (queue == null) return;

                    GameEntry.LogicData.BuildingData.BuildQueueHelpReq(queue.BindBuildNo, queue.QueueUid, (resp) =>
                    {
                        var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(queue.BindBuildNo);
                        buildingModule?.OnUnionHelpChange(Build.QueueType.BuildTech, false);

                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLang(80400010)
                        });

                        OnRefreshUnderState();
                    });
                });

                //刷新2个和3个的位置大小
                if (!GameEntry.LogicData.TechData.IsCanBuyLine3())
                {
                    m_goLine1.transform.localScale = new Vector3(1, 1, 1);
                    m_goLine2.transform.localScale = new Vector3(1, 1, 1);
                    m_goLine3.transform.localScale = new Vector3(0, 0, 0);


                    RectTransform rect1 = m_goLine1.GetComponent<RectTransform>();
                    rect1.anchoredPosition = new Vector3(-150, 60, 0);
                    RectTransform rect2 = m_goLine2.GetComponent<RectTransform>();
                    rect2.anchoredPosition = new Vector3(290, 60, 0);
                }
                else
                {
                    m_goLine1.transform.localScale = new Vector3(0.8f, 0.8f, 1);
                    m_goLine2.transform.localScale = new Vector3(0.8f, 0.8f, 1);
                    m_goLine3.transform.localScale = new Vector3(0.8f, 0.8f, 1);

                    RectTransform rect1 = m_goLine1.GetComponent<RectTransform>();
                    rect1.anchoredPosition = new Vector3(-164, 151, 0);
                    RectTransform rect2 = m_goLine2.GetComponent<RectTransform>();
                    rect2.anchoredPosition = new Vector3(267, 151, 0);
                    RectTransform rect3 = m_goLine3.GetComponent<RectTransform>();
                    rect3.anchoredPosition = new Vector3(-164, 20, 0);
                }
                //帮助和加速切换
                btnAdd.gameObject.SetActive(!isCanHelp);
                btnHelp.gameObject.SetActive(isCanHelp);
                index += 1;
            }
        }
        protected override void OnClose(bool isShutdown, object userData)
        {
            Timers.Instance.Remove("OnRefreshUnderState");
            Timers.Instance.Remove("OnRefreshTab2State");
            GameEntry.Event.Unsubscribe(TechChangeEventArgs.EventId, OnTechChangeUpdate);
            GameEntry.Event.Unsubscribe(PrivilegeChangeEventArgs.EventId, OnPrivilegeChange);
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            // 刷新科技项显示
            RefreshTechItems();
        }
        //购买礼包
        private void OnBtnGiftClick()
        {
            if (!GameEntry.LogicData.TechData.IsBuyLine2())
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuyTechLineForm);
                Close();
                return;
            }
            if (!GameEntry.LogicData.TechData.IsBuyLine3())
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuyTechLineForm);
                Close();
            }
        }
        private void OnBtnCloseClick()
        {
            GameEntry.UI.CloseUIForm(this);
        }
        private void OnBtnReturnClick()
        {
            m_goDetailPanel.SetActive(false);
        }
        private void OnBtnTestClick()
        {
            curShowPanelList.Values.ToList().ForEach(x => x.ClearLine());
        }
        private void OnBtnQueue1Click()
        {
            TechQueue module = GameEntry.LogicData.TechData.GetTechQueueLine1();
            if (module != null)
            {
                if (module.IsFinish())
                {
                    GameEntry.LogicData.TechData.CompeleteTech(module);
                }
            }
        }
        private void OnBtnQueue2Click()
        {
            TechQueue module = GameEntry.LogicData.TechData.GetTechQueueLine2();
            if (module != null)
            {
                if (module.IsFinish())
                {
                    GameEntry.LogicData.TechData.CompeleteTech(module);
                }
            }
        }
        private void OnBtnQueue3Click()
        {

        }
        /// <summary>
        /// 初始化科技数据
        /// </summary>
        private void InitTechData()
        {
            m_techDataDict.Clear();

            // 获取科技类型配置
            List<tech_type> techTypes = GameEntry.LDLTable.GetTable<tech_type>();
            if (techTypes == null || techTypes.Count == 0)
            {
                Debug.LogError("[UITechForm] 获取科技类型配置失败");
                return;
            }

            // 获取科技等级配置
            List<tech_config> techLevels = GameEntry.LDLTable.GetTable<tech_config>();
            if (techLevels == null || techLevels.Count == 0)
            {
                Debug.LogError("[UITechForm] 获取科技等级配置失败");
                return;
            }

            //获取玩家科技数据
            //Dictionary<int, int> playerTechLevels = GameEntry.LogicData.TechData.GetAllTechLevels();

            //初始化科技数据
            foreach (var techType in techTypes)
            {
                // 创建科技数据
                TechItemData techData = new TechItemData
                {
                    Id = techType.id,
                    type = techType.id,
                    Name = ToolScriptExtend.GetLang(techType.tech_type_title),
                    Icon = techType.tech_type_picture,
                    CurrentLevel = GameEntry.LogicData.TechData.GetLevelByType(techType.id),
                    MaxLevel = GameEntry.LogicData.TechData.GetMaxLevelByType(techType.id),
                    desc_id = techType.desc_id ?? new List<int>(),
                    IsLocked = techType.unlock_demand.Count > 0 && GameEntry.LogicData.TechData.GetTechIslock(techType.unlock_demand),
                    unlock_demand = techType.unlock_demand
                };
                m_techDataDict.Add(techType.id, techData);
            }
            //根据解锁状态排序m_techDataDict
            m_techDataDict = m_techDataDict.OrderBy(x => x.Value.IsLocked).ToDictionary(x => x.Key, x => x.Value);
        }

        /// <summary>
        /// 生成科技项
        /// </summary>
        private void GenerateTechItems()
        {
            // 清空现有的科技项
            foreach (var item in m_techItems)
            {
                Destroy(item);
            }
            m_techItems.Clear();

            // 获取content容器
            Transform contentTransform = m_scrollview.transform.Find("view/content");
            if (contentTransform == null)
            {
                Debug.LogError("[UITechForm] 找不到content容器");
                return;
            }

            // 按照ID排序
            var sortedTechData = m_techDataDict.Values;//.Values.OrderBy(t => t.Id).ToList();

            // 生成新的科技项
            foreach (var techData in sortedTechData)
            {
                GameObject techItem = Instantiate(m_goItem, contentTransform);
                techItem.SetActive(true);

                // 设置科技项数据
                techData.obj = techItem;
                UpdateTechItem(techItem, techData);
                m_techItems.Add(techItem);
            }

            // 更新布局
            LayoutRebuilder.ForceRebuildLayoutImmediate(contentTransform.GetComponent<RectTransform>());
        }

        /// <summary>
        /// 刷新科技项显示
        /// </summary>
        private void RefreshTechItems()
        {
            // 更新科技数据
            InitTechData();

            // 更新科技项显示
            for (int i = 0; i < m_techItems.Count; i++)
            {
                var techData = m_techDataDict.Values.ElementAt(i);
                UpdateTechItem(m_techItems[i], techData);
            }
        }

        /// <summary>
        /// 更新单个科技项
        /// </summary>
        private void UpdateTechItem(GameObject techItem, TechItemData techData)
        {
            // 根据提供的预制体结构获取组件
            Image bgImage = techItem.transform.Find("bg").GetComponent<Image>();
            Image iconImage = techItem.transform.Find("icon").GetComponent<Image>();
            Text nameText = techItem.transform.Find("name").GetComponent<Text>();
            Text pre = techItem.transform.Find("pre").GetComponent<Text>();
            GameObject caidaiObj = techItem.transform.Find("caidai").gameObject;
            GameObject caidaiObj2 = techItem.transform.Find("caidai2").gameObject;
            Text txtCaidai = techItem.transform.Find("caidai/txtcaidai")?.GetComponent<Text>();
            Text txtCaidai2 = techItem.transform.Find("caidai2/txtcaidai")?.GetComponent<Text>();
            GameObject tuijian = techItem.transform.Find("tuijian")?.gameObject;
            GameObject line = techItem.transform.Find("line")?.gameObject;
            UIText tiaojian1 = techItem.transform.Find("tiaojian1").GetComponent<UIText>();
            UIText tiaojian2 = techItem.transform.Find("tiaojian2").GetComponent<UIText>();

            // 设置图标
            iconImage.SetImage(techData.Icon);

            // 设置名称
            nameText.text = techData.Name;
            int level = techData.CurrentLevel; //GameEntry.LogicData.TechData.GetLevelByType(techData.type);
            int total = techData.MaxLevel;//GameEntry.LogicData.TechData.GetMaxLevelByType(techData.type);
            bool isMax = level >= total;
            // 设置进度文本
            tiaojian1.text = "";
            tiaojian2.text = "";
            pre.text = "";
            if (pre != null)
            {
                float progress = total > 0 ? (float)level / total * 100 : 0;
                pre.text = $"{progress:0}%";
            }
            
            if (techData.IsLocked)
            {
                if(techData.desc_id.Count > 0)
                {
                    tiaojian1.text = ToolScriptExtend.GetLang(techData.desc_id[0]);
                }
                if(techData.desc_id.Count > 1)
                {
                    tiaojian1.text = ToolScriptExtend.GetLang(techData.desc_id[0]);
                    tiaojian2.text = ToolScriptExtend.GetLang(techData.desc_id[1]);
                }
                pre.text = "";
                bgImage.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_dikuang1.png");
            }else
            {
               
                bgImage.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_dikuang2.png");
            }

        
            txtCaidai.text = ToolScriptExtend.GetLang(1100473);
            txtCaidai2.text = ToolScriptExtend.GetLang(1100474);
            // 设置彩带显示（用于特殊标记，如推荐升级）
            tech_config tuijian1Config = GameEntry.LogicData.TechData.GetSortEconomicsConfig();
            if (tuijian1Config != null)
            {
                bool showEconomics = !techData.IsLocked && !isMax && techData.Id == (int)tuijian1Config?.tech_type;
                caidaiObj.SetActive(showEconomics);
            }
            else
            {
                caidaiObj.SetActive(false);
            }
            tech_config tuijian2Config = GameEntry.LogicData.TechData.GetSortMilitaryConfig();
            if(tuijian2Config != null)
            {
                bool showMilitary = !techData.IsLocked && !isMax && techData.Id == (int)tuijian2Config?.tech_type;
                caidaiObj2.SetActive(showMilitary);
            }
            else
            {
                caidaiObj2.SetActive(false);
            }

            // 添加点击事件
            Button itemButton = techItem.GetComponent<Button>();
            if (itemButton == null)
            {
                itemButton = techItem.AddComponent<Button>();
            }

            itemButton.onClick.RemoveAllListeners();
            itemButton.onClick.AddListener(() => OnTechItemClick(techData));
        }
        
        /// <summary>
        /// 科技项点击事件
        /// </summary>
        private void OnTechItemClick(TechItemData techData)
        {

            // if (techData.IsLocked)
            // {
            //     // 显示未解锁提示
            //     GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            //     {
            //         Content = $"Unlock at player level {techData.UnlockLevel}"
            //     });
            //     return;
            // }

            // if (techData.CurrentLevel >= techData.MaxLevel)
            // {
            //     // 显示已达最大等级提示
            //     GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            //     {
            //         Content = "Already at max level"
            //     });
            //     return;
            // }
            // 打开科技详情界面
            if (techData.IsLocked) return;
            OpenTechDetailUI(techData);
        }

        /// <summary>
        /// 打开科技详情界面
        /// </summary>
        private void OpenTechDetailUI(TechItemData techData)
        {
            // 这里应该打开科技详情界面，显示详细信息和升级按钮
            // 示例代码，实际实现需要根据游戏UI系统调整
            // GameEntry.UI.OpenUIForm(EnumUIForm.UITechDetailForm, new TechDetailParams()
            // {
            //     TechId = techData.Id,
            //     Name = techData.Name,
            //     Icon = techData.Icon,
            //     Description = techData.Description,
            //     CurrentLevel = techData.CurrentLevel,
            //     MaxLevel = techData.MaxLevel,
            //     CurrentEffect = techData.CurrentEffect,
            //     NextEffect = techData.NextEffect,
            //     UpgradeCost = techData.UpgradeCost,
            //     OnUpgradeSuccess = RefreshTechItems
            // });
            curShowPanelList.Clear();
            m_scrollviewDetail.verticalNormalizedPosition = 1;
            var count = GameEntry.LogicData.TechData.GetTypeLine(techData.Id);
            for (int i = 0; i < m_transDetailContent.childCount; i++)
            {
                var obj = m_transDetailContent.GetChild(i).gameObject;
                obj.SetActive(false);
            }
            for (int i = 0; i < count; i++)
            {
                if (i >= m_transDetailContent.childCount)
                {
                    var obj = Instantiate(m_goDetailItem, m_transDetailContent);
                    TechDetailParamsModel model = new TechDetailParamsModel();
                    model.Line = i;
                    model.obj = obj;
                    if (!curShowPanelList.ContainsKey(i))
                    {
                        curShowPanelList.Add(i, model);
                    }
                    model.type = techData.type;
                    model.Init();
                    model.OnRefresh(CurBuildingModule);
                }
                else
                {
                    var obj = m_transDetailContent.GetChild(i).gameObject;
                    TechDetailParamsModel model = new TechDetailParamsModel();
                    model.obj = obj;
                    if (!curShowPanelList.ContainsKey(i))
                    {
                        curShowPanelList.Add(i, model);
                    }
                    model.SetData(techData.type, i);
                    model.Init();
                    model.OnRefresh(CurBuildingModule);
                    m_transDetailContent.GetChild(i).gameObject.SetActive(true);
                }

            }
            //刷新线条
            OnRefreshLine();
            //刷新二层状态
            //OnRefreshTab2State();
            //Debug.LogError(count);
            m_goDetailPanel.SetActive(true);
        }

        public void OnRefreshLine()
        {
            curShowPanelList.Values.ToList().ForEach(x => x.ClearLine());
            foreach (var item in curShowPanelList)
            {
                item.Value.LineToNext(curShowPanelList);
            }

        }
        private void OnBtnSpeedUpClick(int lineIndex)
        {
            if (CurBuildingModule == null) return;
            itemsubtype itemSubType = itemsubtype.itemsubtype_researchspeedup;
            int techBuildId = 0;
            switch (lineIndex)
            {
                case 1:
                    techBuildId = 2201;
                    break;
                case 2:
                    techBuildId = 2202;
                    break;
                case 3:
                    techBuildId = 2203;
                    break;
                default:
                    break;
            }
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSpeedUpForm, new OpenSpeedUpParam(CurBuildingModule, itemSubType,itemid.itemid_nil,techBuildId));
        }
    }

    /// <summary>
    /// 一层科技链
    /// </summary>
    public class TechItemData
    {
        public int Id { get; set; }
        public int type { get; set; }
        public string Name { get; set; }
        public string Icon { get; set; }
        public int CurrentLevel { get; set; }
        public int MaxLevel { get; set; }
        public bool IsLocked { get; set; }
        public List<int> unlock_demand { get; set; }
        public List<int> desc_id { get; set; }
        public GameObject obj;
        //public List<item_produce> UpgradeCost { get; set; }

        //public bool isCurIndexByTechGroup(int group)
        //{
            //tech_type config = GameEntry.LDLTable.GetTableById<tech_type>(group);
            //return GameEntry.LogicData.TechData.GetCurIndexByTechGroup(group) == Id;
        //}
    }

    /// <summary>
    /// 二层科技详情参数
    /// </summary>
    public class TechDetailParamsModel
    {
        public int Line { get; set; }
        public GameObject obj { get; set; }
        public int type { get; set; }
        //public List<item_produce> UpgradeCost { get; set; }
        public System.Action OnUpgradeSuccess { get; set; }
        public TechSingleModel leftItem { get; set; }
        public TechSingleModel rightItem { get; set; }
        public TechSingleModel midItem { get; set; }
        public Transform underMid { get; set; }
        public Transform underLeft { get; set; }
        public Transform underRight { get; set; }
        public void SetLeftItem(TechSingleModel model)
        {
            leftItem = model;
        }
        public void ClearLine()
        {
            if (underLeft != null)
            {
                for (int i = 0; i < underLeft.childCount; i++)
                {
                    var child = underLeft.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
            if (underRight != null)
            {
                for (int i = 0; i < underRight.childCount; i++)
                {
                    var child = underRight.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
            if (underMid != null)
            {
                for (int i = 0; i < underMid.childCount; i++)
                {
                    var child = underMid.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
        }
        public void ShowLine(List<GameObject> lis, int TechId)
        {

        }
        public void SetRightItem(TechSingleModel model)
        {
            rightItem = model;
        }
        public void SetMidItem(TechSingleModel model)
        {
            midItem = model;
        }
        public void SetData(int _type, int line)
        {
            Line = line;
            type = _type;
        }
        public void Init()
        {
            underMid = obj.transform.Find("underMid");
            underLeft = obj.transform.Find("underLeft");
            underRight = obj.transform.Find("underRight");
        }
        public void OnRefresh(BuildingModule buildModule)
        {
            var data = GameEntry.LogicData.TechData.GetTechShowConfig(type, Line + 1);

            if (data == null) return;
            leftItem = data.leftItem;
            rightItem = data.rightItem;
            midItem = data.midItem;
            if (obj != null)
            {
                underMid = obj.transform.Find("underMid");
                underLeft = obj.transform.Find("underLeft");
                underRight = obj.transform.Find("underRight");

                Transform leftObj = obj.transform.Find("itemLeft");
                Transform rightObj = obj.transform.Find("itemRight");
                Transform midObj = obj.transform.Find("itemMid");
                UIButton leftBtn = leftObj.Find("bg").GetComponent<UIButton>();
                UIButton rightBtn = rightObj.Find("bg").GetComponent<UIButton>();
                UIButton midBtn = midObj.Find("bg").GetComponent<UIButton>();
                leftBtn.onClick.RemoveAllListeners();
                rightBtn.onClick.RemoveAllListeners();
                midBtn.onClick.RemoveAllListeners();
                leftBtn.onClick.AddListener(() =>
                {
                    if (leftItem != null)
                    {
                        OnClickSingleTech(leftItem.techId,buildModule);
                    }
                });
                rightBtn.onClick.AddListener(() =>
                {
                    if (rightItem != null)
                    {
                        
                        OnClickSingleTech(rightItem.techId,buildModule);
                    }
                });
                midBtn.onClick.AddListener(() =>
                {
                    if (midItem != null)
                    {
                        OnClickSingleTech(midItem.techId,buildModule);
                    }
                });
                leftObj.gameObject.SetActive(leftItem != null);
                rightObj.gameObject.SetActive(rightItem != null);
                midObj.gameObject.SetActive(midItem != null);
                if (leftItem != null) leftItem.OnRefresh2(leftObj.gameObject);
                if (rightItem != null) rightItem.OnRefresh2(rightObj.gameObject);
                if (midItem != null) midItem.OnRefresh2(midObj.gameObject);
            }
        }
        public void OnClickSingleTech(int techId,BuildingModule _curBuildingModule)
        {
            //改
            TechDetail param = new TechDetail();
            param.techGroup = techId;
            param._curBuildingModule = _curBuildingModule;
            
            //当前正在研究
            if (GameEntry.LogicData.TechData.IsTechingByTechGroup(techId))
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
                return;
            }
            
            //当前满级
            if (GameEntry.LogicData.TechData.IsMaxLevel(techId))
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
                return;
            }

            GameEntry.UI.OpenUIForm(EnumUIForm.UITechUpDetailForm,param);
        }
        public void LineToNext(Dictionary<int, TechDetailParamsModel> dic)
        {
            List<int> targetList = new List<int>();
            int curPos = 0;
            for (int i = 1; i <= 3; i++)
            {
                int TechID = 0;
                switch (i)
                {
                    case 1:
                        TechID = leftItem?.techId ?? 0;
                        break;
                    case 2:
                        TechID = midItem?.techId ?? 0;
                        break;
                    case 3:
                        TechID = rightItem?.techId ?? 0;
                        break;
                    default:
                        break;
                }
                if (TechID > 0)
                {
                    var config = GameEntry.LogicData.TechData.GetTechShowByTechId(TechID);
                    if (config != null)
                    {
                        curPos = int.Parse(config.offset.Split("|")[1]);
                        Line = int.Parse(config.offset.Split("|")[0]);
                        string demand_tech_id = config.demand_tech_id;

                        if (!string.IsNullOrEmpty(demand_tech_id))
                        {
                            var arr = demand_tech_id.Split('|');
                            for (int ii = 0; ii < arr.Length; ii++)
                            {
                                int targetTechId = int.Parse(arr[ii]);
                                targetList.Add(targetTechId);
                            }
                        }
                        else
                        {
                            continue;
                        }
                        //Debug.LogError(Line);
                        switch (curPos)
                        {
                            case 1:
                                for (int i1 = 0; i1 < targetList.Count; i1++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i1]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i1]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underLeft.transform.Find(name);
                                        showLine.gameObject.SetActive(true);

                                        tech_config config1 = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(targetList[i1], GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(targetList[i1])+1);
                                        var showLineLight = GameEntry.LogicData.TechData.CanUpGrade(config1);
                                        string name_light = "light" + (curPos - targetPos).ToString();
                                        var showLineLightObj = underLeft.transform.Find(name_light);
                                        showLineLightObj.gameObject.SetActive(showLineLight);
                                        //Debug.LogError(name);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            case 2:
                                for (int i2 = 0; i2 < targetList.Count; i2++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i2]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i2]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underMid.transform.Find(name);
                                        showLine.gameObject.SetActive(true);
                                        //Debug.LogError(name);
                                        tech_config config2 = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(targetList[i2], GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(targetList[i2])+1);
                                        var showLineLight = GameEntry.LogicData.TechData.CanUpGrade(config2);
                                        string name_light = "light" + (curPos - targetPos).ToString();
                                        var showLineLightObj = underMid.transform.Find(name_light);
                                        showLineLightObj.gameObject.SetActive(showLineLight);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            case 3:
                                for (int i3 = 0; i3 < targetList.Count; i3++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i3]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i3]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underRight.transform.Find(name);
                                        showLine.gameObject.SetActive(true);
                                        //Debug.LogError(name);
                                        tech_config config3 = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(targetList[i3], GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(targetList[i3])+1);
                                        var showLineLight = GameEntry.LogicData.TechData.CanUpGrade(config3);
                                        string name_light = "light" + (curPos - targetPos).ToString();
                                        var showLineLightObj = underRight.transform.Find(name_light);
                                        showLineLightObj.gameObject.SetActive(showLineLight);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }
    //单个科技
    public class TechSingleModel
    {
        public int techId { get; set; }
        public string icon { get; set; }
        public int currentLevel { get; set; }
        public int maxLevel { get; set; }
        public bool isLocked { get; set; }
        public TechSingleModel(int techid)
        {
            techId = techid;
        }
        public void OnRefresh2(GameObject obj)
        {
            if (techId == 0) return;
            UIText progress = obj.transform.Find("progress").GetComponent<UIText>();
            Transform workTime = obj.transform.Find("workTime");
            UIImage tuijian = obj.transform.Find("tuijian").GetComponent<UIImage>();
            tuijian.gameObject.SetActive(false);
            GameObject maxObj = obj.transform.Find("max").gameObject;
            UIImage bg = obj.transform.Find("bg").GetComponent<UIImage>();
            tech_config config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techId,GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techId)+1);
            bool isCanUp = true;
            if (config != null)
            {
                if (GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techId) > 0)
                {
                    isCanUp = true;
                }
                else
                {
                    isCanUp = GameEntry.LogicData.TechData.CanUpGrade(config);
                }
 
            }
           
            if(isCanUp)
            {
                bg.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_tubiao2.png");
            }
            else
            {
                bg.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_tubiao3.png");
            }
            if (workTime != null)
            {
                workTime.gameObject.SetActive(false);
            }
            if (progress != null)
            {
                int level = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techId);
                int max = GameEntry.LogicData.TechData.GetTechLevelMax(techId);
                if (level >= max)
                {
                    maxObj.SetActive(true);
                    progress.text = "";
                }
                else
                {
                    maxObj.SetActive(false);
                   progress.text = $"{level}/{max}";
                }
                
            }
            tech_config tuijian1Config = GameEntry.LogicData.TechData.GetSortEconomicsConfig();
            if (tuijian1Config != null)
            {
                bool showEconomics = !isLocked && techId == tuijian1Config?.tech_group;
                if(showEconomics)
                {
                    tuijian.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_jiaobiao_dianzan1.png");
                    tuijian.gameObject.SetActive(true);   
                }    
            }

            tech_config tuijian2Config = GameEntry.LogicData.TechData.GetSortMilitaryConfig();
            if (tuijian2Config != null)
            {
                bool showMilitary = !isLocked && techId == tuijian2Config?.tech_group;
                if(showMilitary)
                {
                    tuijian.SetImage("Sprite/ui_jianzhu_yanjiuzx/jianzhu_yanjiuzx_jiaobiao_dianzan2.png");
                    tuijian.gameObject.SetActive(true);   
                }
            }


            //队列1  //改
            TechQueue queueModule = null;
            TechQueue queue1 = GameEntry.LogicData.TechData.GetTechQueueLine1();
            TechQueue queue2 = GameEntry.LogicData.TechData.GetTechQueueLine2();
            TechQueue queue3 = GameEntry.LogicData.TechData.GetTechQueueLine3();

            if (queue1 != null && queue1.curTechGroup == techId)
            {
                queueModule = queue1;
            }
            else if (queue2 != null && queue2.curTechGroup == techId)
            {
                queueModule = queue2;
            }
            else if (queue3 != null && queue3.curTechGroup == techId)
            {
                queueModule = queue3;
            }
            UIText txtTime = workTime.Find("time").GetComponent<UIText>();
            UIText num = workTime.Find("num").GetComponent<UIText>();
            if (queueModule != null)
            {
                if (queueModule.BindBuildNo == 2201)
                {
                    num.text = "1st";
                }else if(queueModule.BindBuildNo == 2202)
                {
                    num.text = "2st";
                }
                else if (queueModule.BindBuildNo == 2203)
                {
                    num.text = "3st";
                }
                if (queueModule.curTechGroup == techId)
                {
                    workTime.gameObject.SetActive(true);
                    if (queueModule.IsFinish())
                    {
                        txtTime.text = ToolScriptExtend.GetLang(1100130);
                    }
                    else
                    {
                        float currentRemaining = queueModule.GetRemainTime();

                        if (currentRemaining <= 0)
                        {
                            txtTime.text = txtTime.text = ToolScriptExtend.GetLang(1100130);
                        }
                        else
                        {
                            txtTime.text = TimeHelper.FormatGameTimeWithDays((int)currentRemaining);
                        }
                        // 创建定时器，每秒更新倒计时
                        Timers.Instance.Add("OnRefreshTab2State", 1f, (param) =>
                        {
                            if (txtTime != null)
                            {
                                float currentRemaining = queueModule.GetRemainTime();
                                if (currentRemaining <= 0)
                                {
                                    txtTime.text = txtTime.text = ToolScriptExtend.GetLang(1100130);
                                    Timers.Instance.Remove("OnRefreshTab2State");
                                }
                                else
                                {
                                    txtTime.text = TimeHelper.FormatGameTimeWithDays((int)currentRemaining);
                                }
                            }
                        }, -1); // -1表示无限循环 
                    }
                }
            }
        }
    }
}
