using System.Collections.Generic;
using Game.Hotfix.Config;
using Hero;
using UnityEngine;

namespace Game.Hotfix
{
    public enum SkillActionState
    {
        Begin,
        Action,
        Finish
    }
    
    public abstract class HeroSkillActionBase
    {
        private float m_TimeDuration;
        private bool m_Running = false;
        protected SkillActionState m_ActionState = SkillActionState.Finish;

        protected float StartTime;
        protected float runningStartTime;
        protected HeroBattleSKill m_HeroBattleSKill;

        public HeroSkillActionBase(HeroBattleSKill heroBattleSKill, float startTime)
        {
            m_HeroBattleSKill = heroBattleSKill;
            StartTime = startTime;
            m_Running = false;
            m_ActionState = SkillActionState.Finish;
        }

        public void Begin()
        {
            runningStartTime = StartTime;
            m_TimeDuration = 0;
            m_Running = true;
            m_ActionState = SkillActionState.Begin;
            OnBegin();
        }

        public void Tick(float dt)
        {
            if (m_ActionState == SkillActionState.Begin)
            {
                m_TimeDuration += dt;
                if (m_TimeDuration >= runningStartTime)
                {
                    DoAction();
                    m_ActionState = SkillActionState.Action;
                }
            }else if (m_ActionState == SkillActionState.Action)
            {
                if (CanFinish())
                {
                    DoFinish();
                    m_ActionState = SkillActionState.Finish;
                }
            }
            OnTick(dt);
        }

        protected void DoFinish()
        {
            m_Running = false;
            OnFinish();
        }

        protected abstract void OnFinish();

        protected abstract float DoAction();

        protected abstract void OnBegin();

        protected abstract void OnTick(float dt);

        protected abstract bool CanFinish();
    }
}