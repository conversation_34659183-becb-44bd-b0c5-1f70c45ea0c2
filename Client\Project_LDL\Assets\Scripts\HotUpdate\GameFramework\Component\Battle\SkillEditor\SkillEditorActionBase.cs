#if UNITY_EDITOR
using System;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    public class SkillEditorActionBase
    {
        
        
        
        protected SkillEditorPreview m_SkillEditorPreview;
        public SkillEditorActionBase(SkillEditorPreview skillEditorPreview)
        {
            m_SkillEditorPreview = skillEditorPreview;
        }
        
        protected float Max => m_SkillEditorPreview == null?0:m_SkillEditorPreview.skillDuration;

        public virtual void WriteData(skill_show data)
        {
            
        }

        public virtual void ReadData(skill_show data)
        {
            
        }
        
    }
}
#endif