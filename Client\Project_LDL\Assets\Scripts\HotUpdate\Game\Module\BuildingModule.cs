using System;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using HotUpdate.Game.Module.BuildingModules;
using NUnit.Framework;
using UnityEngine;

namespace Game.Hotfix
{
    using BuildingCfg = Config.build_config;

    /// <summary>
    /// 建筑物状态
    /// </summary>
    public enum BuildingState
    {
        //正常状态
        Normal,
        //升级中
        Upgrading,
        //升级完成待领取
        UpgradeComplete,
        //建造中
        UnderConstruction,
        //建造完成待领取
        ConstructionComplete
    }
    /// <summary>
    /// 商店状态
    /// </summary>
    public enum ShopBuildingState
    {
        CanGetReward,    // 可领取
        Update,          // 更新状态
        Remove,          // 移除
    }

    /// <summary>
    /// 幸存者状态
    /// </summary>
    public enum SurvivorChangeState
    {
        Add,             // 添加
        Update,          // 更新状态
        Remove,          // 移除
    }

    /// <summary>
    /// 建筑事件
    /// </summary>
    public enum BuildingModuleEvent
    {
        //建筑物状态变化
        OnResetPosition,
        OnBuildingStateChange,
        OnResourceQueueStateChange,
        OnHospitalQueueStateChange,
        OnSoldierQueueStateChange,
        OnEquipmentQueueStateChange,
        OnShopStateChange,
        OnSurvivorChange,
        OnUnionHelpChange,
        OnWallChange,
        OnTechChange,
    }

    public class BuildingModule
    {
        public static BuildingModule Create(int buildingId, int level, int uid)
        {
            var data = new BuildingModule(buildingId, level, uid);
            return data;
        }

        private EventDispatch<BuildingModuleEvent> m_EventDispatch = new EventDispatch<BuildingModuleEvent>();

        public BuildingCfg buildingCfg;

        private const int BuildingSurvivorCount = 4;

        // 建筑物id
        public int BuildingId { get; set; }

        //唯一ID
        public int UID { get; set; }

        //等级
        public int LEVEL { get; set; }

        // 建筑物资源路径
        public string BuildingResourcePath
        {
            get
            {
                var prefabCfg = GetBuildingPrefabCfg();
                if (prefabCfg != null)
                {
                    return prefabCfg.pre_location;
                }
                return "";
            }
        }

        // 建筑物名称
        public string BuildingName { get; set; }

        /// <summary>
        /// 获取 EL 类型
        /// </summary>
        /// <returns></returns>
        public Type GetBuildingEntityLogicType()
        {
            buildtype buildType = buildingCfg.build_type;
            return buildType switch
            {
                buildtype.buildtype_72 => typeof(EL_TrainStation),
                buildtype.buildtype_team => typeof(EL_BuildingTeam),
                buildtype.buildtype_researchcenter => typeof(EL_BuildingTech),
                buildtype.buildtype_truck => typeof(EL_BuildingWagon),
                _ => typeof(EL_Building),
            };
        }

        // grid位置
        // public Vector2Int GridPos { get; set; }

        private Vector2Int m_GridPos;

        public List<Vector2Int> Area { get; set; }

        // 建筑物图标
        public string BuildingIcon
        {
            get
            {
                build_level buildingLevelCfg = GetBuildingLevelCfg(LEVEL);
                return buildingLevelCfg.ui_picture;
            }
        }

        public BuildingState BuildingState { get; set; }

        //幸存者列表
        public List<uint> m_SurvivorList;
        public List<uint> SurvivorList
        {
            get
            {
                m_SurvivorList.Sort((a, b) =>
                {
                    SurvivorMoudle aMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(a);
                    SurvivorMoudle bMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(b);
                    return bMoudle.Quality.CompareTo(aMoudle.Quality);
                });
                return m_SurvivorList;
            }
        }

        public BuildingModule(int buildingId, int level, int uid)
        {
            this.BuildingId = buildingId;
            this.UID = uid;
            this.buildingCfg = Game.GameEntry.LDLTable.GetTableById<BuildingCfg>(buildingId);
            this.LEVEL = level;
            // this.buildingCfg.cover_area.width
            BuildingName = ToolScriptExtend.GetLang(this.buildingCfg.name);

            //初始化尺寸
            Area = new List<Vector2Int>();
            //初始化幸存者列表
            m_SurvivorList = new List<uint>();
            for (int i = 0; i < this.buildingCfg.cover_area.length; i++)
            {
                for (int j = 0; j < this.buildingCfg.cover_area.width; j++)
                {
                    Area.Add(new Vector2Int(i, j));
                }
            }

            //初始化状态
            TryChangeState();

        }

        /// <summary>
        /// 获取建筑物的状态
        /// </summary>
        /// <returns></returns>
        public BuildingState GetBuildingState()
        {
            return BuildingState;
        }

        public buildtype GetBuildingType()
        {
            return buildingCfg.build_type;
        }

        public bool CanMove()
        {
            if (LEVEL == 0)
                return false;
            return buildingCfg?.build_ismove == buildismove.buildismove_can;
        }

        public void SetGridPos(int x, int y)
        {
            m_GridPos.x = x;
            m_GridPos.y = y;

            SendEvent(BuildingModuleEvent.OnResetPosition, this);
        }

        public Vector2Int GetGridPos()
        {
            return m_GridPos;
        }

        public Vector3 GetWorldPosition()
        {
            return new Vector3(m_GridPos.x, 0, m_GridPos.y);
        }

        public Vector3 GetWorldCenterPosition()
        {
            var offset = this.GetOffsetCenter();
            return new Vector3(m_GridPos.x + offset.x, 0, m_GridPos.y + offset.y);
        }

        /// <summary>
        /// 宽
        /// </summary>
        /// <returns></returns>
        public int GetGridAreaW()
        {
            return buildingCfg.cover_area.width;
        }

        /// <summary>
        /// 长
        /// </summary>
        /// <returns></returns>
        public int GetGridAreaL()
        {
            return buildingCfg.cover_area.length;
        }

        /// <summary>
        /// 围栏宽度
        /// </summary>
        /// <returns></returns>
        public int GetGridRepairAreaW()
        {
            return buildingCfg.bulid_up_show.width;
        }

        /// <summary>
        /// 围栏长度
        /// </summary>
        /// <returns></returns>
        public int GetGridRepairAreaL()
        {
            return buildingCfg.bulid_up_show.length;
        }

        public Vector3 GetGridRepairOffset()
        {
            var configOffset = buildingCfg.bulid_up_offset;
            var offset = Vector3.zero;
            if (configOffset.Count > 0)
                offset.x = configOffset[0];
            if (configOffset.Count > 1)
                offset.y = configOffset[1];
            if (configOffset.Count > 2)
                offset.z = configOffset[2];
            return offset;
        }

        public Vector2 GetOffsetCenter()
        {
            return new Vector2(GetGridAreaL() / 2f, GetGridAreaW() / 2f);
        }

        public bool IsInArea(int x, int y, int baseX, int baseY)
        {
            for (int i = 0; i < this.Area.Count; i++)
            {
                if (baseX + Area[i].x == x && baseY + Area[i].y == y)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否可以升级
        /// </summary>
        /// <returns></returns>
        public bool UpgradeAble()
        {
            var levelCnt = GameEntry.LogicData.BuildingData.GetBuildingLevelCnt(buildingCfg.build_type);
            return levelCnt > 1;
        }

        /// <summary>
        ///  是否已经是最大等级
        /// </summary>
        /// <returns></returns>
        public bool IsMaxLevel()
        {
            if (GameEntry.LogicData.BuildingData.HasNextLevel(buildingCfg.build_type, LEVEL + 1))
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 是否可以被派遣
        /// </summary>
        /// <returns></returns>
        public bool DispatchAble()
        {
            return buildingCfg.survivor_num > 0;
        }

        public virtual List<int> GetMenuList()
        {
            if (LEVEL == 0 && GetBuildingState() == BuildingState.Normal)
            {
                return new List<int>();
            }

            List<int> menuList = new List<int>();
            if (DispatchAble())//是否可以派遣
            {
                menuList.Add((int)EnumBuildingMenu.Btn1002);
            }
            if (UpgradeAble())
            {
                BuildingState buildingState = GetBuildingState();
                if (buildingState == BuildingState.Upgrading)
                {
                    menuList.Add((int)EnumBuildingMenu.Btn1024);
                }
                else if (buildingState == BuildingState.Normal)
                {
                    if (IsMaxLevel())
                        menuList.Add((int)EnumBuildingMenu.Btn1003);
                    else
                        menuList.Add((int)EnumBuildingMenu.Btn1023);
                }
            }

            bool needSort = true;
            switch (buildingCfg.build_type)
            {
                case buildtype.buildtype_nil:
                    break;
                case buildtype.buildtype_headquarters:
                    menuList.Add((int)EnumBuildingMenu.Btn1005);
                    menuList.Add((int)EnumBuildingMenu.Btn1006);
                    break;
                case buildtype.buildtype_barrack:
                    menuList.Add((int)EnumBuildingMenu.Btn1009);
                    break;
                case buildtype.buildtype_warehouseiron:
                    break;
                case buildtype.buildtype_warehousegrain:
                    break;
                case buildtype.buildtype_warehousegold:
                    break;
                case buildtype.buildtype_gate:
                    menuList.Add((int)EnumBuildingMenu.Btn1004);
                    break;
                case buildtype.buildtype_team:
                    menuList.Add((int)EnumBuildingMenu.Btn1008);
                    break;
                case buildtype.buildtype_iron:
                    break;
                case buildtype.buildtype_grain:
                    break;
                case buildtype.buildtype_gold:
                    break;
                case buildtype.buildtype_builder:
                    break;
                case buildtype.buildtype_tavern:
                    menuList.Add((int)EnumBuildingMenu.Btn1012);
                    break;
                case buildtype.buildtype_talenthall:
                    break;
                case buildtype.buildtype_shop:
                    break;
                case buildtype.buildtype_exp:
                    break;
                case buildtype.buildtype_equipstone:
                    break;
                case buildtype.buildtype_screw:
                    break;
                case buildtype.buildtype_uavcomponent:
                    break;
                case buildtype.buildtype_union:
                    menuList.Add((int)EnumBuildingMenu.Btn1015);
                    menuList.Add((int)EnumBuildingMenu.Btn1016);
                    break;
                case buildtype.buildtype_ground:
                    menuList.Add((int)EnumBuildingMenu.Btn1010);
                    break;
                case buildtype.buildtype_researchcenter:
                    BuildingState buildingState = GetBuildingState();
                    if (!(buildingState == BuildingState.Upgrading))
                    {
                        TechQueue queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)BuildingId) as TechQueue;
                        if (queueModule != null && !queueModule.IsFinish())
                        {
                            menuList.Add((int)EnumBuildingMenu.Btn1025);
                        }
                    }

                    menuList.Add((int)EnumBuildingMenu.Btn1019);
                    break;
                case buildtype.buildtype_hospital:
                    menuList.Add((int)EnumBuildingMenu.Btn1011);
                    break;
                case buildtype.buildtype_equipfactory:
                    menuList.Add((int)EnumBuildingMenu.Btn1014);
                    break;
                case buildtype.buildtype_spotter:
                    break;
                case buildtype.buildtype_guardtower:
                    break;
                case buildtype.buildtype_tankcenter:
                    var heroPromotionUnlock = GameEntry.LDLTable.GetTableById<hero_promotion_unlock>(1);
                    demand demand = heroPromotionUnlock.unlock;
                    if (LEVEL >= demand.build_level_demand)
                    {
                        menuList.Add((int)EnumBuildingMenu.Btn1018);
                    }
                    break;
                case buildtype.buildtype_missilecenter:
                    heroPromotionUnlock = GameEntry.LDLTable.GetTableById<hero_promotion_unlock>(2);
                    demand = heroPromotionUnlock.unlock;
                    if (LEVEL >= demand.build_level_demand)
                    {
                        menuList.Add((int)EnumBuildingMenu.Btn1018);
                    }
                    break;
                case buildtype.buildtype_aircraftcenter:
                    heroPromotionUnlock = GameEntry.LDLTable.GetTableById<hero_promotion_unlock>(3);
                    demand = heroPromotionUnlock.unlock;
                    if (LEVEL >= demand.build_level_demand)
                    {
                        menuList.Add((int)EnumBuildingMenu.Btn1018);
                    }
                    break;
                case buildtype.buildtype_flag:
                    break;
                case buildtype.buildtype_chip:
                    break;
                case buildtype.buildtype_action:
                    break;
                case buildtype.buildtype_predominance:
                    break;
                case buildtype.buildtype_eventground:
                    break;
                case buildtype.buildtype_decoration:
                    break;
                case buildtype.buildtype_arena:
                    needSort = false;
                    menuList.Add((int)EnumBuildingMenu.Btn1020);
                    menuList.Add((int)EnumBuildingMenu.Btn1003);
                    break;
                case buildtype.buildtype_uavcenter:
                    needSort = false;
                    menuList.Add((int)EnumBuildingMenu.Btn1017);
                    menuList.Add((int)EnumBuildingMenu.Btn1003);
                    break;
                case buildtype.buildtype_commandpost:
                    break;
                case buildtype.buildtype_truck:
                    menuList.Add((int)EnumBuildingMenu.Btn1001);
                    menuList.Add((int)EnumBuildingMenu.Btn1003);
                    break;
                case buildtype.buildtype_radar:
                    break;
                case buildtype.buildtype_firstaid:
                    break;
                case buildtype.buildtype_bench:
                    break;
                case buildtype.buildtype_luckypool:
                    break;
                case buildtype.buildtype_redcranetatue:
                    break;
                case buildtype.buildtype_ginkgogreen:
                    break;
                case buildtype.buildtype_ginkgoyellow:
                    break;
                case buildtype.buildtype_ginkgoorange:
                    break;
                case buildtype.buildtype_72:
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            if (needSort)
            {
                GameEntry.LogicData.BuildingData.SortMenuList(menuList);
            }
            return menuList;
        }

        /// <summary>
        /// 菜单被点击事件
        /// </summary>
        /// <param name="menuCfg"></param>
        public virtual void OnMenuClick(build_menubutton menuCfg)
        {
            Debug.Log("OnMenuClick:" + menuCfg.id);
            EnumUIForm? uiForm = menuCfg.goto_menu switch
            {
                open_ui.ui_name_1 => EnumUIForm.UIBuildingWagonForm,
                open_ui.ui_name_2 => EnumUIForm.UIBuildingDispatchForm,
                open_ui.ui_name_3 => EnumUIForm.UIBuildingMaxLevelForm,
                open_ui.ui_name_4 => EnumUIForm.UIBuildingWallForm,
                open_ui.ui_name_8 => EnumUIForm.UITeamForm,
                open_ui.ui_name_9 => EnumUIForm.UITrainingSoldiersForm,
                open_ui.ui_name_10 => EnumUIForm.UIBuildingGround,
                open_ui.ui_name_11 => EnumUIForm.UITreatSoldiersForm,
                open_ui.ui_name_12 => EnumUIForm.UIRecruitForm,
                open_ui.ui_name_14 => EnumUIForm.UIEquipmentFactoryForm,
                open_ui.ui_name_17 => EnumUIForm.UIBuildingUAVForm,
                open_ui.ui_name_18 => EnumUIForm.UIHeroHonorForm,
                open_ui.ui_name_19 => EnumUIForm.UITechForm,
                open_ui.ui_name_20 => EnumUIForm.UIPeakArenaForm,
                open_ui.ui_name_23 => EnumUIForm.UIBuildingDetailForm,
                open_ui.ui_name_24 => EnumUIForm.UIBuildingSpeedUpForm,
                _ => null,
            };
            if (uiForm != null)
                if (uiForm == EnumUIForm.UIBuildingSpeedUpForm)
                {
                    itemsubtype openItemSubType = itemsubtype.itemsubtype_nil;
                    if (menuCfg.id == (int)EnumBuildingMenu.Btn1024)
                    {
                        openItemSubType = itemsubtype.itemsubtype_buildspeedup;
                    }
                    else if (menuCfg.id == (int)EnumBuildingMenu.Btn1025)
                    {
                        openItemSubType = itemsubtype.itemsubtype_researchspeedup;
                    }

                    if (openItemSubType != itemsubtype.itemsubtype_nil)
                    {
                        if (openItemSubType == itemsubtype.itemsubtype_researchspeedup)
                        {
                            GameEntry.UI.OpenUIForm(uiForm.Value, new OpenSpeedUpParam(this, openItemSubType, itemid.itemid_nil, (int)BuildingId));
                        }
                        else
                        {
                           GameEntry.UI.OpenUIForm(uiForm.Value, new OpenSpeedUpParam(this, openItemSubType)); 
                        }  
                    }
                }
                else if (uiForm == EnumUIForm.UITrainingSoldiersForm)
                {
                    SoldierQueue soldierQueue = GameEntry.LogicData.QueueData.GetSoldierTrainQueueModule((uint)this.BuildingId);
                    var isFinish = soldierQueue?.IsFinish() ?? false;
                    if (isFinish)
                    {
                        uint soldierNum = soldierQueue.SoldierNum;
                        int soldierQueueSoldierNo = (int)soldierQueue.SoldierNo;
                        SoldierTrainingQueueFinishReq((result) =>
                        {
                            if (result == null)
                            {
                                return;
                            }
                            uint realGetNum = soldierNum;
                            if (result.Queue != null)
                            {
                                uint newSoldierNum = result.Queue.Qa.SoldierNum;
                                realGetNum = soldierNum - newSoldierNum;
                                OnSoldierQueueStateChange(new SoldierParams(SoldierQueueChangeState.Update));
                            }
                            Entity gameEntity = GameEntry.Entity.GetGameEntity(this.UID);
                            bool isAdd = realGetNum > 0;
                            SoldierChangeType changeType = isAdd ? SoldierChangeType.Add : SoldierChangeType.Reduce;
                            OnSoldierQueueStateChange(new SoldierParams(SoldierQueueChangeState.SoldierChange, changeType, soldierQueueSoldierNo, (int)realGetNum));
                            GameEntry.HUD.ShowHUD(gameEntity, EnumHUD.HUDClaimResource, false,
                                new HUDClaimResourceParam(0, (int)realGetNum, soldierQueueSoldierNo));
                        });
                    }
                    else
                    {

                        GameEntry.UI.OpenUIForm(uiForm.Value, this);
                    }
                }
                else if (uiForm == EnumUIForm.UIHeroHonorForm)
                {
                    int openId = 0;
                    if (GetBuildingType() == buildtype.buildtype_tankcenter)
                    {
                        openId = 1;
                    }
                    else if (GetBuildingType() == buildtype.buildtype_missilecenter)
                    {
                        openId = 2;
                    }
                    else
                    {
                        openId = 3;
                    }
                    GameEntry.UI.OpenUIForm(uiForm.Value, openId);
                }
                else if (uiForm == EnumUIForm.UITeamForm)
                {
                    UITeamFormParam param = new UITeamFormParam();
                    param.Index = BuildingId % 100 - 1;
                    param.TeamFormType = UITeamFormType.Common;
                    GameEntry.UI.OpenUIForm(uiForm.Value, param);
                }
                else if (uiForm == EnumUIForm.UIBuildingWagonForm)
                {
                    bool isEnd = GameEntry.LogicData.PvePathData.IsFinish;
                    // 没全通关跳转到当前挑战关卡
                    if (!isEnd)
                    {
                        GameEntry.LogicData.PvePathData.LookAtNextStepObj();
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(uiForm.Value, this);
                    }
                }
                else
                {
                    GameEntry.UI.OpenUIForm(uiForm.Value, this);
                }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = "功能暂未开放",
                });
            }
        }

        public void OnClick()
        {
            Debug.Log("OnClick:");
        }

        public build_level GetBuildingLevelCfg(int? lv = null)
        {
            var level = lv ?? LEVEL;
            build_level buildLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, level);
            return buildLevelCfg;
        }

        public innercity_buildpre GetBuildingPrefabCfg(int? lv = null)
        {
            build_level buildLevel = GetBuildingLevelCfg(lv);
            if (buildLevel != null)
            {
                bool isTeam4 = this.BuildingId == 704;
                int preID = buildLevel.pre_id;
                if (isTeam4)
                    preID = 70400;
                innercity_buildpre buildLevelCfg =
                    GameEntry.LDLTable.GetTableById<innercity_buildpre>(preID);
                if (buildLevelCfg != null)
                {
                    return buildLevelCfg;
                }
                else
                {
                    Debug.LogError(
                        $"innercity_buildpre config not found. level:'{lv}' buildingId:'{buildLevel.pre_id}'  ");
                }
            }
            return null;
        }

        /// <summary>
        /// 来自网络的更新
        /// </summary>
        /// <param name="build"></param>
        public void UpdateInfo(Build.Build build)
        {
            LEVEL = (int)build.BuildLevel;
            SetGridPos((int)build.X, (int)build.Y);
            GameEntry.Event.Fire(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEventArgs.Create(this));
        }

        public Build.Build ToBuild()
        {
            Build.Build build = new Build.Build();
            build.BuildNo = (uint)BuildingId;
            build.BuildLevel = (uint)LEVEL;
            build.X = (uint)GetGridPos().x;
            build.Y = (uint)GetGridPos().y;
            return build;
        }

        public void OnBuildingStateChange()
        {
            TryChangeState();
        }

        public void OnResourceQueueStateChange(ResourceQueueChangeState state)
        {
            SendEvent(BuildingModuleEvent.OnResourceQueueStateChange, state);
        }

        public void OnSoldierQueueStateChange(SoldierParams soldierParams)
        {
            SendEvent(BuildingModuleEvent.OnSoldierQueueStateChange, soldierParams);
        }

        public void OnEquipmentQueueStateChange(EquipmentQueueChangeState state)
        {
            SendEvent(BuildingModuleEvent.OnEquipmentQueueStateChange, state);
        }

        public void OnShopStateChange(ShopBuildingState state)
        {
            SendEvent(BuildingModuleEvent.OnShopStateChange, state);
        }

        public void OnSurvivorChange(SurvivorChangeState state)
        {
            SendEvent(BuildingModuleEvent.OnSurvivorChange, state);
        }

        public void OnUnionHelpChange(QueueType queueType, bool isAdd)
        {
            SetUnionHelp(queueType, isAdd);
            SendEvent(BuildingModuleEvent.OnUnionHelpChange, queueType);
        }

        public void OnTechChange(TechChangState state)
        {
            SendEvent(BuildingModuleEvent.OnTechChange, state);

            var queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)BuildingId);
            if (state == TechChangState.Add && queueModule != null && queueModule.Help == 0)
            {
                OnUnionHelpChange(QueueType.BuildTech, true);
            }
            else if (state == TechChangState.Finish)
            {
                OnUnionHelpChange(QueueType.BuildTech, false);
            }
        }

        public void TryChangeState()
        {
            var targetState = BuildingState.Normal;
            var queueModule = GameEntry.LogicData.QueueData.GetBuildQueueModule((uint)BuildingId);
            if (queueModule != null)
            {
                //判断状态
                if (queueModule.QueueType == QueueType.BuildCreate)
                {
                    targetState = queueModule.IsFinish()
                        ? BuildingState.ConstructionComplete
                        : BuildingState.UnderConstruction;
                }
                else if (queueModule.QueueType == QueueType.BuildUpgrade)
                {
                    targetState = queueModule.IsFinish()
                        ? BuildingState.UpgradeComplete
                        : BuildingState.Upgrading;
                }
            }

            if (BuildingState != targetState)
            {
                BuildingState = targetState;
                SendEvent(BuildingModuleEvent.OnBuildingStateChange, this);

                if (BuildingState == BuildingState.Upgrading && queueModule != null)
                {
                    var upgradingQueue = queueModule as UpgradingQueue;
                    if (upgradingQueue.Help == 0 && upgradingQueue.BuildLevel > 1 && upgradingQueue.GetRemainTime() > 0)
                    {
                        OnUnionHelpChange(QueueType.BuildUpgrade, true);
                    }
                }
                else if (BuildingState == BuildingState.UpgradeComplete)
                {
                    OnUnionHelpChange(QueueType.BuildUpgrade, false);
                }
            }
        }

        public void OnHospitalQueueStateChange(HospitalSoldierParams param)
        {
            SendEvent(BuildingModuleEvent.OnHospitalQueueStateChange, param);
        }

        public void BuildQueueFinishReq()
        {
            var queue = GameEntry.LogicData.QueueData.GetBuildQueueModule((uint)BuildingId);
            if (queue != null)
            {
                GameEntry.LogicData.BuildingData.BuildQueueFinishReq((uint)BuildingId, queue.QueueUid, (result) =>
                {
                    if (result != null && result.Build != null)
                    {
                        build_level buildingLevelCfg =
                            GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(this.buildingCfg.build_type,
                                (int)result.Build.BuildLevel);
                        if (buildingLevelCfg != null && buildingLevelCfg.exp > 0)
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingCompleteForm, new UIBuildingCompleteFormParam(this));
                            var offset = GetOffsetCenter();
                            Entity entity = GameEntry.Entity.GetGameEntity(this.UID);
                            if (entity != null)
                            {
                                FlyResManager.UIFlyWorld2Ui((int)itemid.itemid_5, 10,
                                    entity.transform.position + new Vector3(offset.x, 0, offset.y));
                            }
                        }
                        BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById((uint)BuildingId);
                        if (buildingModule is BuildingSoldierTraining soldierTraining)
                        {
                            soldierTraining.UpdateSoldierUnLockLevel();
                        }
                    }
                });
            }
        }

        public void SoldierTreatQueueFinishReq(Action callBack = null)
        {
            var queue = GameEntry.LogicData.QueueData.GetHospitalQueue();
            if (queue != null)
            {
                List<SoldierModule> oldTreatList = queue.m_TreatSoldierList;
                GameEntry.LogicData.BuildingData.BuildQueueFinishReq((uint)BuildingId, queue.QueueUid, (result) =>
                {
                    Dictionary<int, int> treatDic = new Dictionary<int, int>();
                    int totalTreat = 0;
                    if (oldTreatList.Count > 0)
                    {
                        for (int i = 0; i < oldTreatList.Count; i++)
                        {
                            SoldierModule soldierModule = oldTreatList[i];
                            totalTreat += (int)soldierModule.Num;
                            treatDic[(int)soldierModule.Id] = (int)soldierModule.Num;
                        }
                    }
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLangFormat(1100185, totalTreat.ToString()),
                    });
                    OnHospitalQueueStateChange(new HospitalSoldierParams(HospitalQueueChangeState.TreatComplete, treatDic));
                    callBack?.Invoke();
                });
            }
        }

        public void SoldierTrainingQueueFinishReq(Action<BuildQueueResult> callBack = null)
        {
            var queue = GameEntry.LogicData.QueueData.GetSoldierTrainQueueModule((uint)BuildingId);
            if (queue != null)
            {
                GameEntry.LogicData.BuildingData.BuildQueueFinishReq((uint)BuildingId, queue.QueueUid, (result) =>
                {
                    //todo 训练完成
                    callBack?.Invoke(result);
                });
            }
        }

        public void EquipmentQueueFinishReq(Action callBack = null)
        {
            var queue = GameEntry.LogicData.QueueData.GetEquipmentQueueModule((uint)BuildingId);
            if (queue != null)
            {
                GameEntry.LogicData.BuildingData.BuildQueueFinishReq((uint)BuildingId, queue.QueueUid, (result) =>
                {
                    //todo 制造装备完成
                    callBack?.Invoke();
                });
            }
        }

        public bool CanUpGrade()
        {
            bool isMaxLevel = IsMaxLevel();
            if (isMaxLevel)
            {
                return false;
            }

            if (LEVEL == 0)
            {
                return true;
            }

            build_level nextBuildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, LEVEL + 1);
            bool isBuildDemandUnLock = GetIsBuildingDemandUnLock();

            List<cost> buildCost = nextBuildingLevelCfg.build_cost;
            int conditionNum = 0;
            for (int i = 0; i < buildCost.Count; i++)
            {
                cost cost = buildCost[i];
                itemid itemId = cost.item_id;
                long itemNum = cost.num;
                long count = GameEntry.LogicData.BagData.GetAmountById(itemId);
                if (count >= itemNum)
                {
                    conditionNum += 1;
                }
            }

            return isBuildDemandUnLock && conditionNum != 0 && conditionNum >= buildCost.Count;
        }

        public bool GetIsBuildingDemandUnLock()
        {
            build_level nextBuildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, LEVEL + 1);
            foreach (int demandId in nextBuildingLevelCfg.build_demand)
            {
                bool demandUnlock = ToolScriptExtend.GetDemandUnlock(demandId);
                if (demandUnlock == false)
                {
                    return false;
                }
            }

            return true;
        }

        public int GetBuildingAttrValueByAttrType(attributes_type attributesType)
        {
            build_level buildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, LEVEL);
            for (var i = 0; i < buildingLevelCfg.build_level_attributes.Count; i++)
            {
                var attribute = buildingLevelCfg.build_level_attributes[i];
                if (attribute.attributes_type == attributesType)
                {
                    return attribute.value;
                }
            }

            return 0;
        }

        public void AddSurvivorList(uint survivorId)
        {
            bool exists = m_SurvivorList.Exists(s => s == survivorId);
            if (!exists)
            {
                m_SurvivorList.Add(survivorId);
            }
        }

        public void UpdateSurvivorList(List<uint> changeList)
        {
            m_SurvivorList.Clear();
            for (var i = 0; i < changeList.Count; i++)
            {
                m_SurvivorList.Add(changeList[i]);
            }
        }

        // 检查是否有可派遣
        public bool CheckSurvivorDispatch()
        {
            if (LEVEL == 0 && GetBuildingState() == BuildingState.Normal)
            {
                return false;
            }
            bool isFull = m_SurvivorList.Count >= BuildingSurvivorCount;
            if (!isFull)
            {
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.FindSurvivorMoudleCanDispatch(GetBuildingType());
                return survivorMoudle != null;
            }
            else
            {
                var betterIndex = GetSurvivorIsHaveDispatchBetter();
                return betterIndex != -1;
            }
            return false;
        }

        // 查找是是否有更高品质的派遣
        public int GetSurvivorIsHaveDispatchBetter()
        {
            for (var i = 0; i < m_SurvivorList.Count; i++)
            {
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.FindSurvivorMoudleHaveBetter(GetBuildingType(), m_SurvivorList[i]);
                if (survivorMoudle != null)
                {
                    return i;
                }
            }

            return -1;
        }

        // 查找是是否有幸存者可升级
        public bool GetSurvivorIsHaveDispatchUpStar()
        {
            for (var i = 0; i < m_SurvivorList.Count; i++)
            {
                SurvivorMoudle survivorMoudle = GameEntry.LogicData.SurvivorData.GetSurvivorModuleById(m_SurvivorList[i]);
                if (survivorMoudle != null && survivorMoudle.GetCanIncreaseStar())
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 联盟帮助列表
        /// </summary>
        public List<QueueType> m_unionHelpList;

        public void SetUnionHelp(QueueType queueType, bool isAdd)
        {
            m_unionHelpList ??= new();

            var isHas = m_unionHelpList.Contains(queueType);
            if (isAdd)
            {
                var isJoin = GameEntry.LogicData.UnionData.IsJoinUnion();
                if (!isHas && isJoin) m_unionHelpList.Add(queueType);
            }
            else
            {
                if (isHas) m_unionHelpList.Remove(queueType);
            }
        }

        #region 事件

        public void AddEventListener(BuildingModuleEvent eventType, EventCallBack eventHandler)
        {
            m_EventDispatch.RegisterEvent(eventType, eventHandler);
        }

        public void RemoveEventListener(BuildingModuleEvent eventType, EventCallBack eventHanlder)
        {
            m_EventDispatch.UnRegisterEvent(eventType, eventHanlder);
        }

        protected void SendEvent(BuildingModuleEvent eventType, object obj)
        {
            m_EventDispatch.PostEvent(eventType, obj);
        }

        #endregion

    }
}