
#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Game.Hotfix.Config;
using OfficeOpenXml;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.Serialization;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    [System.Flags]
    public enum eSkillEditorBattlePosition
    {
        PosL1 = 1 << 1,
        PosL2 = 1 << 2,
        PosL3 = 1 << 3,
        PosL4 = 1 << 4,
        PosL5 = 1 << 5,
        PosR1 = 1 << 6,
        PosR2 = 1 << 7,
        PosR3 = 1 << 8,
        PosR4 = 1 << 9,
        PosR5 = 1 <<10,
        ALL = PosL1 | PosL2 | PosL3 | PosL4 |PosL5 | PosR1 | PosR2 | PosR3 | PosR4 | PosR5
    }
    
    public class SkillEditorPreview : MonoBehaviour
    {
        [Title("导入导出")]
        [PropertyOrder(-5)]
        [FilePath] public string exportPath;

        [LabelText("技能ID")]
        [PropertyOrder(-5)]
        public int opSkillId;
        
        
        #region 数据导入导出
        
        [HorizontalGroup("Split", 0.5f)]
        [PropertyOrder(-5)]
        [Button(ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1)]
        public void ImportData()
        {
            string filePath = exportPath??BattleSettings.setting.ExportSkillShowPath;
            FileInfo file = new FileInfo(filePath);
            if (file.Exists)
            {
                using (ExcelPackage package = new ExcelPackage(file))
                {
                    if (package.Workbook.Worksheets["Sheet1"] == null)
                    {
                        Debug.LogError("未找到Sheet1");
                        return;
                    }
                    ExcelWorksheet worksheet = package.Workbook.Worksheets["Sheet1"];
                    
                    int rowCount = worksheet.Dimension?.Rows ?? 0;
                    bool hasFound = false;
                    for (int row = 2; row <= rowCount; row++)
                    {
                        object cellValue = worksheet.Cells[row, 1].Value;
                        int id;
                        if (cellValue!=null && int.TryParse(cellValue.ToString(),out id))
                        {
                            if (id == opSkillId)
                            {
                                skill_show config = new skill_show();
                                
                                config.id = P<int>(worksheet.Cells[row, 1].Value.ToString());
                                config.duration = P<float>(worksheet.Cells[row, 2].Value.ToString());
                                config.a_start_time = P<float>(worksheet.Cells[row, 3].Value.ToString());
                                config.a_name = P<skill_act_group>(worksheet.Cells[row, 4].Value.ToString());
                                config.e_start_time = P<float>(worksheet.Cells[row, 5].Value.ToString());
                                config.e_effect_id = P<int>(worksheet.Cells[row, 6].Value.ToString());
                                config.e_slot = P<slot>(worksheet.Cells[row, 7].Value.ToString());
                                config.w_start_time = P<float>(worksheet.Cells[row, 8].Value.ToString());
                                config.w_effect_id = P<int>(worksheet.Cells[row, 9].Value.ToString());
                                config.s1_start_time = P<float>(worksheet.Cells[row, 10].Value.ToString());
                                config.s1_sound_id = P<int>(worksheet.Cells[row, 11].Value.ToString());
                                config.s2_start_time = P<float>(worksheet.Cells[row, 12].Value.ToString());
                                config.s2_sound_id = P<int>(worksheet.Cells[row, 13].Value.ToString());
                                config.b_start_time = P<float>(worksheet.Cells[row, 14].Value.ToString());
                                config.b_end_time = P<float>(worksheet.Cells[row, 15].Value.ToString());
                                config.b_trajectory = P<trajectory>(worksheet.Cells[row, 16].Value.ToString());
                                config.b_target_type = P<target_type>(worksheet.Cells[row, 17].Value.ToString());
                                config.b_is_copy = P<bool>(worksheet.Cells[row, 18].Value.ToString());
                                config.b_copy_interval = P<float>(worksheet.Cells[row, 19].Value.ToString());
                                config.b_interval = P<float>(worksheet.Cells[row, 20].Value.ToString());
                                config.b_interval_min = P<float>(worksheet.Cells[row, 21].Value.ToString());
                                config.b_interval_max = P<float>(worksheet.Cells[row, 22].Value.ToString());
                                config.b_fire_slots = P<List<slot>>(worksheet.Cells[row, 23].Value.ToString());
                                config.b_is_seq = P<bool>(worksheet.Cells[row, 24].Value.ToString());
                                config.b_hurt_slots = P<slot>(worksheet.Cells[row, 25].Value.ToString());
                                config.b_count = P<int>(worksheet.Cells[row, 26].Value.ToString());
                                config.b_time_line_type = P<battle_time_ease>(worksheet.Cells[row, 27].Value.ToString());
                                config.b_time_line_param = P<List<int>>(worksheet.Cells[row, 28].Value.ToString());
                                config.b_trajectory_param = P<string>(worksheet.Cells[row, 29].Value.ToString());
                                config.b_muzzle_flash_id = P<int>(worksheet.Cells[row, 30].Value.ToString());
                                config.b_muzzle_flash_match = P<bool>(worksheet.Cells[row, 31].Value.ToString());
                                config.b_effect_id = P<int>(worksheet.Cells[row, 32].Value.ToString());
                                config.b_trailing_id = P<int>(worksheet.Cells[row, 33].Value.ToString());
                                config.b_trailing_time = P<float>(worksheet.Cells[row, 34].Value.ToString());
                                config.b_hurt_effect_id = P<int>(worksheet.Cells[row, 35].Value.ToString());
                                config.b_hurt_effect_all = P<bool>(worksheet.Cells[row, 36].Value.ToString());
                                config.b_bomb_range = P<float>(worksheet.Cells[row, 37].Value.ToString());
                                config.b_hurt_sound_id = P<int>(worksheet.Cells[row, 38].Value.ToString());
                                config.b_bomb_range_x = P<float>(worksheet.Cells[row, 39].Value.ToString());
                                config.b_bomb_range_y = P<float>(worksheet.Cells[row, 40].Value.ToString());

                                skillId = config.id;
                                skillDuration = config.duration;
                                
                                SaAnimation = new SkillEditorActionAnimation(this);
                                SaAnimation.ReadData(config);
                                
                                SaCastBeforeParticle = new SkillEditorActionParticle(this);
                                SaCastBeforeParticle.ReadData(config);

                                SaBullet = new SkillEditorActionBullet(this);
                                SaBullet.ReadData(config);
                                
                                Debug.Log("配置导入成功:" + id + " 第" + row + "行");
                                return;
                            }
                        }
                    }

                    if (!hasFound)
                    {
                        
                        Debug.LogError("未找到指定ID:" + opSkillId);
                    }
                }
            }
            else
            {
                Debug.LogError("未找到文件:" + filePath);
            }
        }

        [PropertyOrder(-5)]
        [VerticalGroup("Split/right")]
        [Button(ButtonSizes.Large), GUIColor(0, 1, 0)]
        public void ExportData()
        {
            var config = EditorDataToConfig();
            string filePath = exportPath;
            FileInfo file = new FileInfo(filePath);
            if (file.Exists)
            {
                using (ExcelPackage package = new ExcelPackage(file))
                {
                    if (package.Workbook.Worksheets["Sheet1"] == null)
                    {
                        Debug.LogError("未找到Sheet1");
                        return;
                    }
                    ExcelWorksheet worksheet = package.Workbook.Worksheets["Sheet1"];
                    
                    int rowCount = worksheet.Dimension?.Rows ?? 0;
                    bool hasFound = false;
                    int? isRowWritable = null;
                    for (int row = 2; row <= rowCount; row++)
                    {
                        object cellValue = worksheet.Cells[row, 1].Value;
                        int id;
                        if (cellValue!=null && int.TryParse(cellValue.ToString(),out id))
                        {
                            if (id == config.id)
                            {
                                hasFound = true;
                                UpdateRowData(worksheet, row, config);
                                package.Save();
                                Debug.Log("配置写入成功:" + id + " 第" + row + "行");
                                return;
                            }
                        }
                        if (string.IsNullOrEmpty(cellValue?.ToString()) && isRowWritable == null)
                        {
                            isRowWritable = row;
                        }
                    }

                    if (!hasFound)
                    {
                        int targetRow = isRowWritable ?? ++rowCount;
                        
                        //如果没有找到 创建一行
                        UpdateRowData(worksheet, targetRow, config);
                        package.Save();
                        Debug.LogError("未找到指定ID:" + config.id + ",写入第" + targetRow + "行");
                    }
                }
            }
            else
            {
                Debug.LogError("未找到文件:" + filePath);
            }
        }
        
        #endregion
        
        [Title("测试")]
        [PropertyOrder(-4)]
        [HideLabel]
        [EnumToggleButtons]
        public eSkillEditorBattlePosition BitmaskEnumField;
        [PropertyOrder(-4)]
        // [VerticalGroup("Split/right")]
        [Button("释放技能",ButtonSizes.Large), GUIColor(1, 1, 0)]
        public void CastSkill()
        {
            var battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            if (battle5V5Component == null)
            {
                Debug.LogError("未找到战斗组件【需要运行游戏】");
                return;
            }

            EntityLogic el = GetComponent<EntityLogic>();
            BattleHero battleHero = battle5V5Component.BattleFiled.TeamCtrl.GetBattleHero(el);
            if (battleHero!=null)
            {
                var skillCtrl = battleHero.HeroBattleSkillCtrl;
                if (skillCtrl.HasSkill(opSkillId))
                    skillCtrl.RemoveSkill(opSkillId);
                
                skill_show config = EditorDataToConfig();
                HeroBattleSKill skill = new HeroBattleSKill(battle5V5Component.BattleFiled, config, battleHero, null);
                skillCtrl.AddSkill(skill);

                //收集目标
                List<EnumBattlePos> targetList = GetTargetList(BitmaskEnumField);

                if (targetList.Count <= 0)
                {
                    Debug.LogError("未指定目标！默认攻击对位");
                    if (battleHero.BattlePos > EnumBattlePos.PosL5)
                        targetList.Add((EnumBattlePos)((int)battleHero.BattlePos - BattleDefine.AttackTeamMaxPos));
                    else
                        targetList.Add((EnumBattlePos)((int)battleHero.BattlePos + BattleDefine.AttackTeamMaxPos));
                }
                skillCtrl.CastSkill(skill.ShowId, targetList);
            }
        }
        
        [Title("技能基础")]
        [LabelText("技能ID")]
        [ReadOnly]
        public int skillId;

        [LabelText("技能持续时间")]
        public float skillDuration;

        [ShowInInspector]
        [Title("动作相关")] [LabelText("动作")] public SkillEditorActionAnimation SaAnimation;
        
        [ShowInInspector]
        [Title("特效相关")] [LabelText("前摇特效")] public SkillEditorActionParticle SaCastBeforeParticle;

        [ShowInInspector]
        [Title("子弹相关")] [LabelText("子弹")] public SkillEditorActionBullet SaBullet;

        [Button("重置")]
        private void Reset()
        {
            SaAnimation = null;
            SaCastBeforeParticle = null;
            SaBullet = null;
            InitBtn();
        }
        
        [OnInspectorInit]
        private void InitBtn()
        {
            if (SaAnimation == null)
                SaAnimation = new SkillEditorActionAnimation(this);
            if(SaCastBeforeParticle == null)
                SaCastBeforeParticle = new SkillEditorActionParticle(this);
            if (SaBullet == null)
                SaBullet = new SkillEditorActionBullet(this);

            exportPath = BattleSettings.setting.ExportSkillShowPath;
        }

        private List<EnumBattlePos> GetTargetList(eSkillEditorBattlePosition mask)
        {
            List<EnumBattlePos> list = new List<EnumBattlePos>();
            
            if (mask.HasFlag(eSkillEditorBattlePosition.PosL1))
                list.Add(EnumBattlePos.PosL1);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosL2))
                list.Add(EnumBattlePos.PosL2);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosL3))
                list.Add(EnumBattlePos.PosL3);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosL4))
                list.Add(EnumBattlePos.PosL4);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosL5))
                list.Add(EnumBattlePos.PosL5);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosR1))
                list.Add(EnumBattlePos.PosR1);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosR2))
                list.Add(EnumBattlePos.PosR2);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosR3))
                list.Add(EnumBattlePos.PosR3);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosR4))
                list.Add(EnumBattlePos.PosR4);
            if (mask.HasFlag(eSkillEditorBattlePosition.PosR5))
                list.Add(EnumBattlePos.PosR5);

            return list;
        }
        
        private skill_show EditorDataToConfig()
        {
            skill_show skillShow = new skill_show();
            skillShow.id = opSkillId;
            skillShow.duration = skillDuration;
            SaAnimation.WriteData(skillShow);
            SaCastBeforeParticle.WriteData(skillShow);
            SaBullet.WriteData(skillShow);
            return skillShow;
        }

        private static void UpdateRowData(ExcelWorksheet worksheet, int row, skill_show config)
        {
            worksheet.Cells[row, 1].Value = F(config.id);
            worksheet.Cells[row, 2].Value = F(config.duration);
            worksheet.Cells[row, 3].Value = F(config.a_start_time);
            worksheet.Cells[row, 4].Value = F(config.a_name);
            worksheet.Cells[row, 5].Value = F(config.e_start_time);
            worksheet.Cells[row, 6].Value = F(config.e_effect_id);
            worksheet.Cells[row, 7].Value = F(config.e_slot);
            worksheet.Cells[row, 8].Value = F(config.w_start_time);
            worksheet.Cells[row, 9].Value = F(config.w_effect_id);
            worksheet.Cells[row, 10].Value = F(config.s1_start_time);
            worksheet.Cells[row, 11].Value = F(config.s1_sound_id);
            worksheet.Cells[row, 12].Value = F(config.s2_start_time);
            worksheet.Cells[row, 13].Value = F(config.s2_sound_id);
            worksheet.Cells[row, 14].Value = F(config.b_start_time);
            worksheet.Cells[row, 15].Value = F(config.b_end_time);
            worksheet.Cells[row, 16].Value = F(config.b_trajectory);
            worksheet.Cells[row, 17].Value = F(config.b_target_type);
            worksheet.Cells[row, 18].Value = F(config.b_is_copy);
            worksheet.Cells[row, 19].Value = F(config.b_copy_interval);
            worksheet.Cells[row, 20].Value = F(config.b_interval);
            worksheet.Cells[row, 21].Value = F(config.b_interval_min);
            worksheet.Cells[row, 22].Value = F(config.b_interval_max);
            worksheet.Cells[row, 23].Value = F(config.b_fire_slots);
            worksheet.Cells[row, 24].Value = F(config.b_is_seq);
            worksheet.Cells[row, 25].Value = F(config.b_hurt_slots);
            worksheet.Cells[row, 26].Value = F(config.b_count);
            worksheet.Cells[row, 27].Value = F(config.b_time_line_type);
            worksheet.Cells[row, 28].Value = F(config.b_time_line_param);
            worksheet.Cells[row, 29].Value = F(config.b_trajectory_param);
            worksheet.Cells[row, 30].Value = F(config.b_muzzle_flash_id);
            worksheet.Cells[row, 31].Value = F(config.b_muzzle_flash_match);
            worksheet.Cells[row, 32].Value = F(config.b_effect_id);
            worksheet.Cells[row, 33].Value = F(config.b_trailing_id);
            worksheet.Cells[row, 34].Value = F(config.b_trailing_time);
            worksheet.Cells[row, 35].Value = F(config.b_hurt_effect_id);
            worksheet.Cells[row, 36].Value = F(config.b_hurt_effect_all);
            worksheet.Cells[row, 37].Value = F(config.b_bomb_range);
            worksheet.Cells[row, 38].Value = F(config.b_hurt_sound_id);
            worksheet.Cells[row, 39].Value = F(config.b_bomb_range_x);
            worksheet.Cells[row, 40].Value = F(config.b_bomb_range_y);
        }

        private static string F(float value) 
        {
            return value.ToString("G4");
        }

        private static string F(int value)
        {
            return value.ToString();
        }

        private static string F(string value)
        {
            return value;
        }

        private static string F<T>(List<T> list)
        {
            return list == null || !list.Any() ? string.Empty : string.Join("|", list);
        }

        private static object F(object value)
        {
            return value;
        }

        private static T P<T>(string obj)
        {
            if (typeof(T) == typeof(string))
            {
                return (T)Convert.ChangeType(obj, typeof(T));
            }else if (typeof(T) == typeof(int))
            {
                if (int.TryParse(obj,out var value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }else if(typeof(T) == typeof(float))
            {
                if(float.TryParse(obj,out var value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }else if(typeof(T) == typeof(bool))
            {
                if(bool.TryParse(obj,out var value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }else if(typeof(T) == typeof(target_type))
            {
                if(Enum.TryParse(obj,out target_type value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }
            else if (typeof(T) == typeof(slot))
            {
                if (Enum.TryParse(obj,out slot value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }
            else if (typeof(T) == typeof(battle_time_ease))
            {
                if (Enum.TryParse(obj,out battle_time_ease value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }
            else if (typeof(T) == typeof(skill_act_group))
            {
                if (Enum.TryParse(obj,out skill_act_group value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }else if (typeof(T) == typeof(trajectory))
            {
                if (Enum.TryParse(obj,out trajectory value))
                    return (T)Convert.ChangeType(value, typeof(T));
            }
            else if (typeof(T) == typeof(List<slot>))
            {
                List<slot> value = new List<slot>();
                var datas = obj.Split("|");
                for (int i = 0; i < datas.Length; i++)
                {
                    if (Enum.TryParse(datas[i], out slot tempSlot))
                        value.Add(tempSlot);
                    else
                        Debug.LogError("枚举转化失败" + datas[i] + " " + typeof(T));
                }
                return (T)Convert.ChangeType(value, typeof(T));
            }else if (typeof(T) == typeof(List<int>))
            {
                List<int> value = new List<int>();
                var datas = obj.Split("|");
                for (int i = 0; i < datas.Length; i++)
                {
                    if(string.IsNullOrEmpty(datas[i]))
                        continue;
                    if (int.TryParse(datas[i], out var temp))
                        value.Add(temp);
                    else
                        Debug.LogError("枚举转化失败" + datas[i] + " " + typeof(T));
                }
                return (T)Convert.ChangeType(value, typeof(T));
            }
            Debug.LogError("类型转化失败:" + obj + "=>type:" + typeof(T));
            return default;
        }
        
    }
    
}

#endif