using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_BuildingTech : EL_Building
    {
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnTechChange, OnTechChange);
            
        }

        protected override void OnAttached(EntityLogic childEntity, Transform parentTransform, object userData)
        {
            //初始化研究中心模型
            TechQueue techQueueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)m_BuildingModule.BuildingId) as TechQueue;
            bool isWorking = techQueueModule != null;
            RefreshBuildingDisplay(isWorking);

            if (techQueueModule != null && !techQueueModule.IsFinish() && techQueueModule.Help == 0)
            {
                m_BuildingModule.OnUnionHelpChange(Build.QueueType.BuildTech, true);
            }
        }

        private void OnTechChange(object obj)
        {
            if (obj is TechChangState state)
            {
                if (state == TechChangState.Add)
                {
                    RefreshBuildingDisplay(true);
                }
                else if (state == TechChangState.Free)
                {
                    RefreshBuildingDisplay(false);
                }
            }
        }

        private void RefreshBuildingDisplay(bool isWorking)
        {
            if (eIdDisplay != null && m_BuildingModule.GetBuildingState() == BuildingState.Normal)
            {
                Entity gameEntity = GameEntry.Entity.GetGameEntity(eIdDisplay.Value);
                if (gameEntity != null)
                {
                    var disPlayEntity = gameEntity.CachedTransform;
                    if (disPlayEntity != null)
                    {
                        GameObject noWorkObj = disPlayEntity.transform.Find("Tech/technology center").gameObject;
                        GameObject workObj = disPlayEntity.transform.Find("Tech/Tech_1").gameObject;
                        noWorkObj.gameObject.SetActive(!isWorking);
                        workObj.gameObject.SetActive(isWorking);
                    }
                }
            }
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
            
            m_BuildingModule.OnTechChange(TechChangState.Remove);
        }
    }
}