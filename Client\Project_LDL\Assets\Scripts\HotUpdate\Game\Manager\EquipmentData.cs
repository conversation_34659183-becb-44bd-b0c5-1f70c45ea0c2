using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public class EquipmentData
    {
        readonly Dictionary<equipposition, List<equipment_config>> equipmentConfig = new();
        readonly List<equipment_config> tempList = new();
        readonly List<equipment_level> tempLevelList = new();
        readonly Dictionary<int, List<equip_attributes>> tempAttrDict = new();
        readonly List<EquipmentModule> equipments = new();
        readonly Dictionary<int, EquipmentModule> equipmentDict = new();

        public List<EquipmentModule> Equipments => equipments;
        public Dictionary<int, EquipmentModule> EquipmentDict => equipmentDict;

        public itemid MakingEquipmentID { get; set; }
        public long MakingEndTime { get; set; }
        public int CurDetailEquipmentID { get; set; }
        public bool InitSort { get; set; }
        public int CurRecommendTeamIndex { get; set; }

        public void Init(Roledata.RoleEquip roleEquip)
        {
            InitEquipmentConfig();

            if (roleEquip != null)
            {
                Equip.EquipChanges equipChanges = new()
                {
                    List = { roleEquip.Equips }
                };

                OnEquipmentChanges(equipChanges);
            }
        }

        #region 接口

        /// <summary>
        /// 根据装备 ID 获取装备数据
        /// </summary>
        /// <param name="id">装备 ID</param>
        /// <returns></returns>
        public EquipmentModule GetEquipmentByID(int id)
        {
            if (equipmentDict.ContainsKey(id))
            {
                return equipmentDict[id];
            }
            return null;
        }

        /// <summary>
        /// 获取排序后的装备列表
        /// </summary>
        /// <returns>装备列表</returns>
        public List<EquipmentModule> GetEquipmentListAndSort()
        {
            if (InitSort)
            {
                int index = 0;
                for (int i = 0; i < equipments.Count; i++)
                {
                    if (equipments[i].id == CurDetailEquipmentID)
                    {
                        index = i;
                        break;
                    }
                }
                for (int i = index - 1; i >= 0; i--)
                {
                    EquipmentModule cur = equipments[index];
                    EquipmentModule pre = equipments[i];
                    if (cur.EquipmentQuality != pre.EquipmentQuality) break;
                    if (cur.Part != pre.Part) break;

                    if (cur.equipment_level > pre.equipment_level)
                    {
                        EquipmentModule temp = equipments[i];
                        equipments[i] = equipments[index];
                        equipments[index] = temp;
                        index = i;
                    }
                    else if (cur.equipment_level == pre.equipment_level)
                    {
                        if (cur.PromotionPhase > pre.PromotionPhase)
                        {
                            EquipmentModule temp = equipments[i];
                            equipments[i] = equipments[index];
                            equipments[index] = temp;
                            index = i;
                        }
                    }
                }
                return equipments;
            }

            // 已穿戴 > 品质 > 部位 > 等级 > 星级
            equipments.Sort((a, b) =>
            {
                int aIsWearing = a.target_id != 0 ? 100000 : 0;
                int bIsWearing = b.target_id != 0 ? 100000 : 0;

                int aQuality = a.EquipmentQuality * 10000;
                int bQuality = b.EquipmentQuality * 10000;

                int aPart = (5 - (int)a.Part) * 1000;
                int bPart = (5 - (int)b.Part) * 1000;

                int aLevel = a.equipment_level * 10;
                int bLevel = b.equipment_level * 10;

                int aStar = a.PromotionPhase;
                int bStar = b.PromotionPhase;

                int aWeight = aIsWearing + aQuality + aPart + aLevel + aStar;
                int bWeight = bIsWearing + bQuality + bPart + bLevel + bStar;

                return bWeight.CompareTo(aWeight);
            });
            InitSort = true;
            return equipments;
        }

        /// <summary>
        /// 获取可晋升的装备列表
        /// </summary>
        /// <returns>装备列表</returns>
        public List<EquipmentModule> GetEquipmentListCanPromote()
        {
            List<EquipmentModule> temp = new();
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].CanPromote)
                {
                    temp.Add(equipments[i]);
                }
            }
            return temp;
        }

        /// <summary>
        /// 根据部位获取穿戴的装备
        /// </summary>
        /// <param name="heroID">英雄 ID</param>
        /// <param name="part">部位</param>
        /// <returns>穿戴中的装备</returns>
        public EquipmentModule GetWearingByPart(itemid heroID, equipposition part)
        {
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].target_id == heroID && equipments[i].Part == part)
                {
                    return equipments[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取穿戴的装备
        /// </summary>
        /// <param name="heroID">英雄 ID</param>
        /// <returns>穿戴中的装备</returns>
        public List<EquipmentModule> GetWearing(itemid heroID, bool isFilterPromote = false)
        {
            List<EquipmentModule> temp = new();
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].target_id == heroID)
                {
                    // 筛选可晋升的装备
                    if (isFilterPromote)
                    {
                        if (!equipments[i].CanPromote) continue;
                    }
                    temp.Add(equipments[i]);
                }
            }
            temp.Sort((a, b) => (int)a.Part - (int)b.Part);
            return temp;
        }

        /// <summary>
        /// 获取未穿戴的装备列表
        /// </summary>
        /// <returns>未穿戴的装备列表</returns>
        public List<EquipmentModule> GetNotWearing(int qualityLimit = 0)
        {
            List<EquipmentModule> temp = new();
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].target_id == 0)
                {
                    if (qualityLimit > 0)
                    {
                        if (equipments[i].EquipmentQuality >= qualityLimit) continue;
                    }
                    temp.Add(equipments[i]);
                }
            }
            temp.Sort((a, b) =>
            {
                int aQuality = a.EquipmentQuality;
                int bQuality = b.EquipmentQuality;

                int aPart = (int)a.Part;
                int bPart = (int)b.Part;

                int aLevel = a.equipment_level;
                int bLevel = b.equipment_level;

                int aStar = a.PromotionPhase;
                int bStar = b.PromotionPhase;

                if (aQuality != bQuality)
                {
                    return aQuality.CompareTo(bQuality);
                }
                else if (aPart != bPart)
                {
                    return aPart.CompareTo(bPart);
                }
                else if (aLevel != bLevel)
                {
                    return aLevel.CompareTo(bLevel);
                }
                else if (aStar != bStar)
                {
                    return aStar.CompareTo(bStar);
                }
                else
                {
                    return 0;
                }
            });
            return temp;
        }

        /// <summary>
        /// 对应部位是否有可用装备
        /// </summary>
        /// <param name="part">部位</param>
        /// <returns>是否有可用装备</returns>
        public bool HasEquipmentWithPart(equipposition part)
        {
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].target_id == 0 && equipments[i].Part == part)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 是否有更高战力的装备
        /// </summary>
        /// <param name="heroID">英雄 ID</param>
        /// <returns>是否有更高战力的装备</returns>
        public bool HasHigherPowerEquipment(itemid heroID)
        {
            List<equipposition> equippositions = new()
            {
                equipposition.equipposition_weapon,
                equipposition.equipposition_armor,
                equipposition.equipposition_chip,
                equipposition.equipposition_radar
            };
            bool hasHigherPowerEquipment = false;
            for (int i = 0; i < equippositions.Count; i++)
            {
                EquipmentModule equipment = GetWearingByPart(heroID, equippositions[i]);
                int power = 0;
                if (equipment != null)
                {
                    power = equipment.Power;
                }

                EquipmentModule higherPowerEquipment = GetHigherPowerEquipment(equippositions[i], power);
                if (higherPowerEquipment != null)
                {
                    hasHigherPowerEquipment = true;
                    break;
                }
            }
            return hasHigherPowerEquipment;
        }

        /// <summary>
        /// 获取更高战力的装备
        /// </summary>
        /// <param name="part">装备部位</param>
        /// <param name="power">当前装备战力</param>
        /// <returns>更高战力的装备</returns>
        public EquipmentModule GetHigherPowerEquipment(equipposition part, int power)
        {
            int maxPower = power;
            EquipmentModule equipmentModule = null;
            for (int i = 0; i < equipments.Count; i++)
            {
                if (equipments[i].target_id == 0 && equipments[i].Part == part && equipments[i].Power > maxPower)
                {
                    maxPower = equipments[i].Power;
                    equipmentModule = equipments[i];
                }
            }
            return equipmentModule;
        }

        /// <summary>
        /// 装备是否解锁
        /// </summary>
        /// <param name="id">装备 ID</param>
        /// <returns>装备解锁状态</returns>
        public bool IsEquipmentUnlock(itemid id, out string buildName, out int buildLevel)
        {
            buildName = string.Empty;
            buildLevel = 0;
            equipment_config equipmentConfig = GetEquipmentConfigByID(id);
            if (equipmentConfig == null) return false;
            demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(equipmentConfig.unlock_demand);
            if (demandConfig == null) return false;
            buildtype buildtype = demandConfig.build_demand.build_type_demand;
            List<build_config> buildConfig = GameEntry.LDLTable.GetTable<build_config>();
            int buildID = 0;
            for (int i = 0; i < buildConfig.Count; i++)
            {
                if (buildConfig[i].build_type == buildtype)
                {
                    buildID = buildConfig[i].id;
                }
            }

            build_config buildConfigByID = GameEntry.LDLTable.GetTableById<build_config>(buildID);
            if (buildConfigByID != null)
            {
                buildName = ToolScriptExtend.GetLang(buildConfigByID.name);
            }

            buildLevel = demandConfig.build_demand.build_level_demand;

            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById((uint)buildID);
            if (buildingModule == null) return false;
            if (buildingModule.LEVEL >= demandConfig.build_demand.build_level_demand)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 材料是否解锁合成
        /// </summary>
        /// <param name="id">材料 ID</param>
        /// <returns>材料合成解锁状态</returns>
        public bool CanMaterialCompose(itemid id, out string buildName, out int buildLevel)
        {
            buildName = string.Empty;
            buildLevel = 0;
            equipment_materials equipmentMaterials = GetEquipmentMaterial(id);
            if (equipmentMaterials == null) return false;
            demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(equipmentMaterials.unlock_demand);
            if (demandConfig == null) return false;
            buildtype buildtype = demandConfig.build_demand.build_type_demand;
            List<build_config> buildConfig = GameEntry.LDLTable.GetTable<build_config>();
            int buildID = 0;
            for (int i = 0; i < buildConfig.Count; i++)
            {
                if (buildConfig[i].build_type == buildtype)
                {
                    buildID = buildConfig[i].id;
                }
            }

            build_config buildConfigByID = GameEntry.LDLTable.GetTableById<build_config>(buildID);
            if (buildConfigByID != null)
            {
                buildName = ToolScriptExtend.GetLang(buildConfigByID.name);
            }

            buildLevel = demandConfig.build_demand.build_level_demand;
            
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById((uint)buildID);
            if (buildingModule == null) return false;

            if (buildingModule.LEVEL >= demandConfig.build_demand.build_level_demand)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        #endregion

        #region 配置表

        /// <summary>
        /// 初始化装备配置
        /// </summary>
        void InitEquipmentConfig()
        {
            List<equipment_config> configs = GameEntry.LDLTable.GetTable<equipment_config>();
            for (int i = 0; i < configs.Count; i++)
            {
                equipposition part = configs[i].position;
                if (!equipmentConfig.ContainsKey(part))
                {
                    List<equipment_config> list = new();
                    equipmentConfig.Add(part, list);
                }
                equipmentConfig[part].Add(configs[i]);
            }
        }

        /// <summary>
        /// 根据部位获取装备配置
        /// </summary>
        /// <param name="part">部位</param>
        /// <returns>装备列表</returns>
        public List<equipment_config> GetEquipmentConfig(equipposition part)
        {
            return equipmentConfig[part];
        }

        /// <summary>
        /// 获取可制造的装备列表
        /// </summary>
        /// <param name="part">部位</param>
        /// <returns>可制造的装备列表</returns>
        public List<equipment_config> GetProducibleEquipmentConfig(equipposition part)
        {
            tempList.Clear();
            for (int i = 0; i < equipmentConfig[part].Count; i++)
            {
                if (equipmentConfig[part][i].producible)
                {
                    tempList.Add(equipmentConfig[part][i]);
                }
            }
            return tempList;
        }

        /// <summary>
        /// 获取可制造的装备图标
        /// </summary>
        /// <returns>装备图标</returns>
        public string GetProducibleEquipmentIcon()
        {
            List<equipposition> parts = new()
            {
                equipposition.equipposition_weapon,
                equipposition.equipposition_armor,
                equipposition.equipposition_chip,
                equipposition.equipposition_radar
            };

            List<equipment_config> temp = new();

            for (int i = 0; i < parts.Count; i++)
            {
                List<equipment_config> configs = GetProducibleEquipmentConfig(parts[i]);
                for (int j = 0; j < configs.Count; j++)
                {
                    equipment_config equipmentConfig = configs[j];

                    // 材料
                    itemid costID = equipmentConfig.cost_materials[0].item_id;
                    long costNum = equipmentConfig.cost_materials[0].num;
                    long count = GameEntry.LogicData.BagData.GetAmountById(costID);

                    // 金币
                    itemid costCoinID = equipmentConfig.cost_coin.item_id;
                    long costCoinNum = equipmentConfig.cost_coin.num;
                    long costCoinCount = GameEntry.LogicData.BagData.GetAmountById(costCoinID);

                    // 是否解锁
                    bool isUnlock = IsEquipmentUnlock(equipmentConfig.id, out string buildName, out int buildLevel);

                    if (isUnlock && count >= costNum && costCoinCount >= costCoinNum)
                    {
                        temp.Add(equipmentConfig);
                    }
                }
            }

            temp.Sort((a, b) =>
            {
                return b.equipment_quality.CompareTo(a.equipment_quality);
            });

            Dictionary<quality, List<equipment_config>> tempDic = new();
            for (int i = 0; i < temp.Count; i++)
            {
                if (!tempDic.ContainsKey(temp[i].equipment_quality))
                {
                    tempDic.Add(temp[i].equipment_quality, new List<equipment_config>());
                }
                tempDic[temp[i].equipment_quality].Add(temp[i]);
            }

            if (tempDic.Count > 0)
            {
                List<equipment_config> list = tempDic.ElementAt(0).Value;
                if (list.Count > 0)
                {
                    int random = UnityEngine.Random.Range(0, list.Count);
                    return ToolScriptExtend.GetItemIcon(list[random].id);
                }
            }
            
            return string.Empty;
        }

        /// <summary>
        /// 根据装备 ID 获取装备配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>装备配置</returns>
        public equipment_config GetEquipmentConfigByID(itemid itemID)
        {
            List<equipment_config> list = GameEntry.LDLTable.GetTable<equipment_config>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].id == itemID)
                {
                    return list[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取装备等级配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>装备等级配置</returns>
        public List<equipment_level> GetEquipmentLevelConfig(itemid itemID)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            tempLevelList.Clear();
            List<equipment_level> list = GameEntry.LDLTable.GetTable<equipment_level>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID)
                {
                    tempLevelList.Add(list[i]);
                }
            }
            return tempLevelList;
        }

        /// <summary>
        /// 获取单条装备等级配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <param name="level">装备等级</param>
        /// <returns>装备等级配置</returns>
        public equipment_level GetEquipmentLevelConfig(itemid itemID, int level)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            List<equipment_level> list = GameEntry.LDLTable.GetTable<equipment_level>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID && list[i].strengthen_lv == level)
                {
                    return list[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取晋升等级配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>晋升等级配置</returns>
        public List<equipment_promotion> GetPromoteLevelConfig(itemid itemID)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            List<equipment_promotion> temp = new();
            List<equipment_promotion> list = GameEntry.LDLTable.GetTable<equipment_promotion>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID)
                {
                    temp.Add(list[i]);
                }
            }
            return temp;
        }

        /// <summary>
        /// 获取单条晋升等级配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <param name="level">装备等级</param>
        /// <returns>晋升等级配置</returns>
        public equipment_promotion GetPromoteLevelConfig(itemid itemID, int level)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            List<equipment_promotion> list = GameEntry.LDLTable.GetTable<equipment_promotion>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID && list[i].promotion_lv == level)
                {
                    return list[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取基础属性配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <param name="level">装备等级</param>
        /// <returns>基础属性</returns>
        public List<equip_attributes> GetBasicProperty(itemid itemID, int level)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            equipment_level levelConfig = GameEntry.EquipmentData.GetEquipmentLevelConfig(itemID, level);
            if (levelConfig == null) return new List<equip_attributes>();
            return levelConfig.basic_attributes;
        }

        /// <summary>
        /// 获取额外属性配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>额外属性</returns>
        public Dictionary<int, List<equip_attributes>> GetExtraProperty(itemid itemID)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            tempAttrDict.Clear();
            List<equipment_level> list = GameEntry.LDLTable.GetTable<equipment_level>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID && list[i].extra_attributes_replace.Count > 0)
                {
                    tempAttrDict.Add(list[i].strengthen_lv, list[i].extra_attributes_replace);
                }
            }
            return tempAttrDict;
        }

        /// <summary>
        /// 获取基础和额外属性配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>基础和额外属性</returns>
        public Dictionary<int, List<equip_attributes>> GetBasicAndExtraProperty(itemid itemID)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            tempAttrDict.Clear();
            equipment_level levelConfig = GameEntry.EquipmentData.GetEquipmentLevelConfig(itemID, 0);
            tempAttrDict.Add(0, levelConfig.basic_attributes);
            List<equipment_level> list = GameEntry.LDLTable.GetTable<equipment_level>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID && list[i].extra_attributes_replace.Count > 0)
                {
                    tempAttrDict.Add(list[i].strengthen_lv, list[i].extra_attributes_replace);
                }
            }
            return tempAttrDict;
        }

        /// <summary>
        /// 获取晋升基础属性配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <param name="level">装备等级</param>
        /// <returns>基础属性</returns>
        public List<equip_attributes> GetPromoteBasicProperty(itemid itemID, int level)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            equipment_promotion levelConfig = GameEntry.EquipmentData.GetPromoteLevelConfig(itemID, level);
            if (levelConfig == null) return new List<equip_attributes>();
            return levelConfig.attributes;
        }

        /// <summary>
        /// 获取晋升额外属性配置
        /// </summary>
        /// <param name="itemID">装备 ID</param>
        /// <returns>额外属性</returns>
        public Dictionary<int, List<equip_attributes>> GetPromoteExtraProperty(itemid itemID)
        {
            if ((int)itemID % 10 == 5) itemID = (itemid)((int)itemID - 1);
            Dictionary<int, List<equip_attributes>> levelExtra = GetExtraProperty(itemID);
            List<equipment_promotion> list = GameEntry.LDLTable.GetTable<equipment_promotion>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].euqipment_id == itemID && list[i].extra_attributes_replace.Count > 0)
                {
                    levelExtra.Add(list[i].promotion_phase, list[i].extra_attributes_replace);
                }
            }
            return levelExtra;
        }

        /// <summary>
        /// 获取装备材料配置
        /// </summary>
        public List<equipment_materials> GetEquipmentMaterialsConfig()
        {
            List<equipment_materials> list = GameEntry.LDLTable.GetTable<equipment_materials>();
            list.Sort((a, b) => b.materials_quality.CompareTo(a.materials_quality));
            return list;
        }

        /// <summary>
        /// 获取材料配置
        /// </summary> 
        /// <param name="itemID">材料 ID</param>
        /// <returns>材料配置</returns>
        public equipment_materials GetEquipmentMaterial(itemid itemID)
        {
            List<equipment_materials> list = GameEntry.LDLTable.GetTable<equipment_materials>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].item_id == itemID)
                {
                    return list[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取合成目标材料
        /// </summary>
        /// <param name="itemID">当前选择的材料 ID</param>
        /// <returns>目标材料</returns>
        public equipment_materials GetComposeTargetMaterial(itemid itemID)
        {
            List<equipment_materials> list = GameEntry.LDLTable.GetTable<equipment_materials>();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].synth_num != null && list[i].synth_num.item_id == itemID)
                {
                    return list[i];
                }
            }
            return null;
        }

        /// <summary>
        /// 获取分解目标材料
        /// </summary>
        /// <param name="itemID">当前选择的材料 ID</param>
        /// <returns>目标材料</returns>
        public equipment_materials GetResolveTargetMaterial(itemid itemID)
        {
            List<equipment_materials> list = GameEntry.LDLTable.GetTable<equipment_materials>();
            itemid temp = 0;
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].item_id == itemID && list[i].break_item != null)
                {
                    temp = list[i].break_item.item_id;
                }
            }

            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].item_id == temp)
                {
                    return list[i];
                }
            }
            return null;
        }


        #endregion

        #region 协议

        /// <summary>
        /// 装备数据变化
        /// </summary>
        /// <param name="equipmentChanges">装备数据</param>
        public void OnEquipmentChanges(Equip.EquipChanges equipmentChanges)
        {
            ColorLog.Pink("装备变化", equipmentChanges);

            // 装备新增或修改
            if (equipmentChanges.List != null)
            {
                for (int i = 0; i < equipmentChanges.List.Count; i++)
                {
                    Equip.Equip equip = equipmentChanges.List[i];
                    EquipmentModule item = GetEquipmentByID((int)equip.Id);
                    if (item != null)
                    {
                        item.SetData(equip);
                    }
                    else
                    {
                        item = new EquipmentModule();
                        item.SetData(equip);
                        equipmentDict.Add((int)equip.Id, item);
                        equipments.Add(item);
                    }
                }
            }

            // 装备删除
            if (equipmentChanges.Deleted != null)
            {
                for (int i = 0; i < equipmentChanges.Deleted.Count; i++)
                {
                    int id = (int)equipmentChanges.Deleted[i];
                    if (equipmentDict.ContainsKey(id))
                    {
                        equipments.Remove(equipmentDict[id]);
                        equipmentDict.Remove(id);
                    }
                }
            }

            GameEntry.Event.Fire(EquipmentChangeEventArgs.EventId, EquipmentChangeEventArgs.Create());
        }

        /// <summary>
        /// 请求装备制造
        /// </summary>
        /// <param name="code">装备 ID</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentProduce(itemid code, uint buildingID, Action<Equip.EquipProduceResp> callback = null)
        {
            Equip.EquipProduceReq req = new()
            {
                Code = (PbGameconfig.itemid)code,
                BuildId = buildingID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipProduce, req, (message) =>
            {
                Equip.EquipProduceResp resp = message as Equip.EquipProduceResp;
                if (resp != null)
                {
                    GameEntry.LogicData.BuildingData.HandleBuildQueueResult(resp.Result);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备拆解
        /// </summary>
        /// <param name="equipmentIDList">装备 ID 列表</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentResolve(List<uint> equipmentIDList, Action<Equip.EquipResolveResp> callback = null)
        {
            Equip.EquipResolveReq req = new()
            {
                EquipIdList = { equipmentIDList }
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipResolve, req, (message) =>
            {
                Equip.EquipResolveResp resp = message as Equip.EquipResolveResp;
                if (resp != null)
                {
                    List<ulong> temp = new();
                    foreach (var item in resp.EquipIdList)
                    {
                        temp.Add(item);
                    }
                    Equip.EquipChanges equipChanges = new()
                    {
                        Deleted = { temp }
                    };
                    OnEquipmentChanges(equipChanges);
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备材料合成
        /// </summary>
        /// <param name="code">材料 ID</param>
        /// <param name="type">合成类型</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentMaterialSynthesis(itemid code, uint type, Action<Equip.EquipMaterialSynthesisResp> callback = null)
        {
            Equip.EquipMaterialSynthesisReq req = new()
            {
                Code = (PbGameconfig.itemid)code,
                OperateType = type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipMaterialSynthesis, req, (message) =>
            {
                Equip.EquipMaterialSynthesisResp resp = message as Equip.EquipMaterialSynthesisResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备材料分解
        /// </summary>
        /// <param name="code">材料 ID</param>
        /// <param name="type">分解类型</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentMaterialResolve(itemid code, uint type, Action<Equip.EquipMaterialResolveResp> callback = null)
        {
            Equip.EquipMaterialResolveReq req = new()
            {
                Code = (PbGameconfig.itemid)code,
                OperateType = type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipMaterialResolve, req, (message) =>
            {
                Equip.EquipMaterialResolveResp resp = message as Equip.EquipMaterialResolveResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备穿戴
        /// </summary>
        /// <param name="heroID">英雄 ID</param>
        /// <param name="equipmentID">装备 ID</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentTakeOn(itemid heroID, uint equipmentID, Action<Equip.EquipTakeOnResp> callback = null)
        {
            Equip.EquipTakeOnReq req = new()
            {
                TargetId = (PbGameconfig.itemid)heroID,
                EquipId = equipmentID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipTakeOn, req, (message) =>
            {
                Equip.EquipTakeOnResp resp = message as Equip.EquipTakeOnResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备卸下
        /// </summary>
        /// <param name="heroID">英雄 ID</param>
        /// <param name="equipmentID">装备 ID</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentTakeOff(itemid heroID, uint equipmentID, Action<Equip.EquipTakeOffResp> callback = null)
        {
            Equip.EquipTakeOffReq req = new()
            {
                TargetId = (PbGameconfig.itemid)heroID,
                EquipId = equipmentID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipTakeOff, req, (message) =>
            {
                Equip.EquipTakeOffResp resp = message as Equip.EquipTakeOffResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备升级
        /// </summary>
        /// <param name="equipmentID">装备 ID</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentLevelUp(uint equipmentID, Action<Equip.EquipLevelUpResp> callback = null)
        {
            Equip.EquipLevelUpReq req = new()
            {
                EquipId = equipmentID,
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipLevelUp, req, (message) =>
            {
                Equip.EquipLevelUpResp resp = message as Equip.EquipLevelUpResp;
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求装备晋升
        /// </summary>
        /// <param name="equipmentID">装备 ID</param>
        /// <param name="callback">回调</param>
        public void RequestEquipmentPromotion(uint equipmentID, Action<Equip.EquipPromotionResp> callback = null)
        {
            Equip.EquipPromotionReq req = new()
            {
                EquipId = equipmentID,
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.EquipPromotion, req, (message) =>
            {
                Equip.EquipPromotionResp resp = message as Equip.EquipPromotionResp;
                callback?.Invoke(resp);
            });
        }

        #endregion
    }
}