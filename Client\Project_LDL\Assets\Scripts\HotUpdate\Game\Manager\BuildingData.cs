using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Build;
using Game.Hotfix.BuildingModules;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using HotUpdate.Game.Module.BuildingModules;
using Roledata;
using Sirenix.Utilities;
using Soldier;
using UnityEngine;
using Worker;
using Random = System.Random;

namespace Game.Hotfix
{
    public class BuildingData:IEnumerable<BuildingModule>
    {
        private List<BuildingModule> m_BuildingList = new List<BuildingModule>();
        private Dictionary<int, BuildingModule> m_BuildingDic = new Dictionary<int, BuildingModule>();

        private Dictionary<int, int> m_BuildingMenuPriority = new Dictionary<int, int>();
        private Dictionary<buildclass, List<build_config>> m_buildByClassDis = new Dictionary<buildclass, List<build_config>>();
        
        //配置相关
        private Dictionary<buildtype, Dictionary<int,build_level>> buildLevelCfg = new Dictionary<buildtype, Dictionary<int,build_level>>();
        
        public BuildingData()
        {
            
        }

        public void Init(Roledata.RoleBuild roleBuild)
        {
            //初始化建筑物配置表
            InitBuildingCfg();
            InitMenuBtnCfg();
            if(TempConnectHelper.Instance.IsOffline())
                //TODO 临时使用 配置表 初始化玩家的所有建筑物
                InitTestBuilding();
            else
                //初始化建筑物数据
                InitBuilding(roleBuild);
        }
        
        public void InitBuildingCfg()
        {
            List<build_level> list = GameEntry.LDLTable.GetTable<build_level>();
            for (int i = 0; i < list.Count; i++)
            {
                var cfg = list[i];
                var buildType = (buildtype)cfg.build_type;
                Dictionary<int,build_level> typeList;
                if (!buildLevelCfg.ContainsKey(buildType))
                {
                    typeList = new Dictionary<int,build_level>();
                    buildLevelCfg[buildType] = typeList;
                }
                typeList = buildLevelCfg[buildType];
                typeList[cfg.level] = cfg;
            }

            List<build_config> buildConfigs = Game.GameEntry.LDLTable.GetTable<build_config>();
            foreach (var buildConfig in buildConfigs)
            {
                if (!m_buildByClassDis.ContainsKey(buildConfig.build_type2))
                {
                    m_buildByClassDis[buildConfig.build_type2] = new List<build_config>();
                }
                m_buildByClassDis[buildConfig.build_type2].Add(buildConfig);
            }
        }

        public void InitMenuBtnCfg()
        {
            List<build_menubutton> list = GameEntry.LDLTable.GetTable<build_menubutton>();
            for (int i = 0; i < list.Count; i++)
            {
                m_BuildingMenuPriority.Add(list[i].id, list[i].priority);
            }
        }
        
        private void InitBuilding(Roledata.RoleBuild roleBuild)
        {
            var buildingList = roleBuild?.Builds;
            if (buildingList == null)
                return;
            for (int i = 0; i < buildingList.Count; i++)
            {
                Build.Build build = buildingList[i];
                BuildingModule buildingModule = CreatBuildingMoudleByType((int)build.BuildNo,(int)build.BuildLevel,Game.GameEntry.Entity.GenerateSerialId());
                buildingModule.SetGridPos((int)build.X, (int)build.Y);
                
                m_BuildingList.Add(buildingModule);
                m_BuildingDic.Add(buildingModule.UID, buildingModule);
                GameEntry.LogicData.GridData.AddBuilding(buildingModule);
            }
        }
        
        public void InitTestBuilding()
        {
            // int buildingUid = 1; 
            List<innercity_initialbuild> buildingList = GameEntry.LDLTable.GetTable<innercity_initialbuild>();
            for (int i = 0; i < buildingList.Count; i++)
            {
                var buildingInitCfg = buildingList[i];
                
                //BuildingModule buildingModule = new BuildingModule(buildingInitCfg.id,buildingInitCfg.initial_lv, Game.GameEntry.Entity.GenerateSerialId());
                BuildingModule buildingModule = CreatBuildingMoudleByType(buildingInitCfg.id,buildingInitCfg.initial_lv,Game.GameEntry.Entity.GenerateSerialId());
                buildingModule.SetGridPos(buildingInitCfg.initial_location.x, buildingInitCfg.initial_location.y);
                
                m_BuildingList.Add(buildingModule);
                m_BuildingDic.Add(buildingModule.UID, buildingModule);
                GameEntry.LogicData.GridData.AddBuilding(buildingModule);
            }
        }

        public BuildingModule CreatBuildingMoudleByType(Build.Build build)
        {
            var tModule = CreatBuildingMoudleByType((int)build.BuildNo, (int)build.BuildLevel,
                Game.GameEntry.Entity.GenerateSerialId());
            tModule.SetGridPos((int)build.X, (int)build.Y);
            return tModule;
        }
        
        public BuildingModule CreatBuildingMoudleByType(int id,int level,int seriaId)
        {
            build_config buildConfig = GameEntry.LDLTable.GetTableById<build_config>(id);
            buildtype b_type = buildConfig.build_type;
            var buildingMoudle = b_type switch
            {
                buildtype.buildtype_gold => new BuildingGold(id, level, seriaId),
                buildtype.buildtype_grain => new BuildingGrain(id, level, seriaId),
                buildtype.buildtype_iron => new BuildingIron(id, level, seriaId),
                buildtype.buildtype_exp => new BuildingExp(id, level, seriaId),
                buildtype.buildtype_barrack => new BuildingSoldierTraining(id, level, seriaId),
                _ => new BuildingModule(id, level, seriaId)
            };
            return buildingMoudle;
        }

        // public void Foreach(Action<BuildingModule> buildAction)
        // {
        //     for (int i = 0; i < m_BuildingList.Count; i++)
        //     {
        //         buildAction(m_BuildingList[i])
        //     }
        // }

        public void SortMenuList(List<int> list)
        {
            list.Sort((a, b) =>
            {
                if (m_BuildingMenuPriority.ContainsKey(a) && m_BuildingMenuPriority.ContainsKey(b))
                {
                    return m_BuildingMenuPriority[a].CompareTo(m_BuildingMenuPriority[b]);
                }
                return 0;
            });
        }
        
        public int GetMainCityLevel()
        {
            BuildingModule buildingModule = GetBuildingModuleById(101);
            return buildingModule.LEVEL;
        }
        
        public BuildingModule GetBuildingModuleById(uint buildingId)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.BuildingId == buildingId)
                {
                    return buildingModule;
                }
            }
            return null;
        }
        
        public List<BuildingModule> GetAllDispatchBuildingModuleListByType(buildtype findType)
        {
            List<BuildingModule> list = new List<BuildingModule>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.GetBuildingType() == findType && buildingModule.SurvivorList.Count > 0)
                {
                    list.Add(buildingModule);
                }
            }
            return list;
        }
        
        public build_level GetBuildingLevelCfg(buildtype type,int? lv = null)
        {
            Dictionary<int,build_level> list;
            var level = lv ?? 0;
            if (buildLevelCfg.TryGetValue(type, out list))
            {
                if (list.ContainsKey(level))
                {
                    return list[level];
                }
                else
                {
                    Debug.LogError(
                        $"build_level config not found in cache. level:'{level}'  buildtype:'{type}'");
                }
            }
            else
            {
                Debug.LogError(
                    $"build_level config not found in cache. BuildType:'{type}'");
            }
            return null;
        }

        public int GetBuildingLevelCnt(buildtype type)
        {
            Dictionary<int,build_level> list;
            if (buildLevelCfg.TryGetValue(type, out list))
            {
                return list.Count;
            }

            return 0;
        }

        public bool HasNextLevel(buildtype type,int? lv = null)
        {
            Dictionary<int,build_level> list;
            var level = lv ?? 0;
            if (buildLevelCfg.TryGetValue(type, out list))
            {
                if (list.ContainsKey(level))
                {
                    return true;
                }
            }
            return false;
        }

        public bool GetBuildingDemandIsUnlock(buildtype buildingType, int level, int num)
        {
            int count = 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildingType && buildingModule.LEVEL >= level)
                {
                    count += 1;
                }
            }
            return count >= num;
        }

        public bool CheckBuildingCanBuildByType(buildclass buildClass,buildtype buildingType)
        {
            if (m_buildByClassDis.TryGetValue(buildClass,out List<build_config> list))
            {
                //List<build_config> findList = new List<build_config>();
                for (int i = 0; i < list.Count; i++)
                {
                    build_config buildConfig = list[i];
                    if (buildConfig.build_type == buildingType)
                    {
                        BuildingModule buildingModule = GetBuildingModuleById((uint)buildConfig.id);
                        bool isShow = false;
                        bool isCanBuild = false;
                        if (buildConfig.view_demand.Count == 0)
                        {
                            isShow = true;
                        }
                        else
                        {
                            for (int k = 0; k < buildConfig.view_demand.Count; k++)
                            {
                                isShow = ToolScriptExtend.GetDemandUnlock(buildConfig.view_demand[k]);
                            }
                        }

                        if (isShow)
                        {
                            if (buildConfig.build_demand.Count == 0)
                            {
                                isCanBuild = true;
                            }
                            else
                            {
                                int totalCount = 0;
                                for (int k = 0; k < buildConfig.build_demand.Count; k++)
                                {
                                    bool isUnlock = ToolScriptExtend.GetDemandUnlock(buildConfig.build_demand[k]);
                                    if (isUnlock)
                                    {
                                        totalCount += 1;
                                    }
                                }

                                isCanBuild = totalCount >= buildConfig.build_demand.Count;
                            }
                        }

                        if (buildingModule == null && isShow && isCanBuild)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        public Dictionary<attributes_type, float> GetAttrbutesConfigByType(buildtype buildType,int level)
        {
            Dictionary<attributes_type,float> buildAttributes = new Dictionary<attributes_type, float>();
            build_level buildingLevelCfg = GetBuildingLevelCfg(buildType, level);
            if (buildingLevelCfg != null)
            {
                for (int i = 0; i < buildingLevelCfg.build_level_attributes.Count; i++)
                {
                    attributes buildLevelAttribute = buildingLevelCfg.build_level_attributes[i];
                    buildAttributes[buildLevelAttribute.attributes_type] = buildLevelAttribute.value_type switch
                    {
                        valuetype.valuetype_1 => buildLevelAttribute.value,
                        valuetype.valuetype_2 => (float)buildLevelAttribute.value/10000,
                        _ => 0
                    };
                }
            }

            return buildAttributes;
        }
        
        //根据加成属性映射对应加成的属性
        public attributes_type GetAttrbutesMappingAdditionType(attributes_type attributesType)
        {
            return attributesType switch
            {
                attributes_type.attributes_type_76 => attributes_type.attributes_type_57,
                attributes_type.attributes_type_77 => attributes_type.attributes_type_58,
                attributes_type.attributes_type_78 => attributes_type.attributes_type_59,
                attributes_type.attributes_type_81 => attributes_type.attributes_type_73,
                attributes_type.attributes_type_70 => attributes_type.attributes_type_140,
                attributes_type.attributes_type_158 => attributes_type.attributes_type_65,
                attributes_type.attributes_type_159 => attributes_type.attributes_type_64,
                attributes_type.attributes_type_160 => attributes_type.attributes_type_66,
                _ => attributes_type.attributes_type_nil
            };
        }

        public int GetCostDiamond(int time)
        {
            int costNum = 0;
            global_setting globalSetting = GameEntry.LDLTable.GetTableById<global_setting>(1);
            string config1 = globalSetting.value[0];
            string config2 = globalSetting.value[1];
            string config3 = globalSetting.value[2];
            string[] condition1 = config1.Split("|");
            string[] condition2 = config2.Split("|");
            string[] condition3 = config3.Split("|");
            int time1 = int.Parse(condition1[0]);
            int cost1 = int.Parse(condition1[1]);
            int time2 = int.Parse(condition2[0]);
            int cost2 = int.Parse(condition2[1]);
            int cost3 = int.Parse(condition3[0]);
            if (time <= time1)
            {
                costNum = (int)Mathf.Ceil((float)time / cost1);
            }
            else if (time > time1 && time < time2)
            {
                costNum = (int)Mathf.Ceil((float)time / cost2);
            }
            else if (time > time2)
            {
                costNum = (int)Mathf.Ceil((float)time / cost3);
            }
            return costNum;
        }
        
        // 满足建造要求的有多少
        public void GetUnLockLimitMax(buildtype buildType, out int limitMax,out List<build_config> unLockList)
        {
            limitMax = 0;
            unLockList = new List<build_config>();
            List<build_config> buildConfigs = GameEntry.LDLTable.GetTable<build_config>();
            for (int i = 0; i < buildConfigs.Count; i++)
            {
                build_config buildConfig = buildConfigs[i];
                if (buildConfig.build_type == buildType)
                {
                    bool isCanBuild = false;
                    if (buildConfig.build_demand.Count == 0)
                    {
                        limitMax += 1;
                        unLockList.Add(buildConfig);
                    }
                    else
                    {
                        for (int j = 0; j < buildConfig.build_demand.Count; j++)
                        {
                            int demandId = buildConfig.build_demand[j];
                            isCanBuild = ToolScriptExtend.GetDemandUnlock(demandId);
                            if (isCanBuild)
                            {
                                limitMax += 1;
                                unLockList.Add(buildConfig);
                            }
                        }
                    }
                }
            }
        }

        public int GetBuildingCountByBuildType(List<build_config> unLockList)
        {
            int totalCount = 0;
            foreach (var buildConfig in unLockList)
            {
                BuildingModule buildingModuleById = GetBuildingModuleById((uint)buildConfig.id);
                if (buildingModuleById != null)
                {
                    totalCount += 1;
                }
            }

            return totalCount;
        }

        public string GetBuildingNameByBuildType(buildtype buildType)
        {
            string buildName = "";
            BuildingModule buildingModule;
            var buildConfigs = GameEntry.LDLTable.GetTable<build_config>();
            for (int i = 0; i < buildConfigs.Count; i++)
            {
                if (buildConfigs[i].build_type == buildType)
                {
                    buildName = ToolScriptExtend.GetLang(buildConfigs[i].name);
                    break;
                }
            }
            return buildName;
        }
        
        public build_config GetBuildingConfigByBuildType(buildtype buildType)
        {
            build_config findCfg = null;
            var buildConfigs = GameEntry.LDLTable.GetTable<build_config>();
            for (int i = 0; i < buildConfigs.Count; i++)
            {
                if (buildConfigs[i].build_type == buildType)
                {
                    findCfg = buildConfigs[i];
                    break;
                }
            }
            return findCfg;
        }

        public void FindBuildingByType(buildtype buildType,bool? needOpen = null, Action action = null)
        {
            BuildingModule findBuild = null;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildType)
                {
                    findBuild = buildingModule;
                    break;
                }
            }

            if (findBuild != null)
            {
                Vector3 buildPos = findBuild.GetWorldCenterPosition();
                GameEntry.Camera.LookAtPosition(buildPos, () =>
                {
                    if (needOpen != null && needOpen.Value)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingDetailForm,findBuild);
                    }
                });
            }
            else
            {
                action?.Invoke();
                Debug.LogError($"找不到该类型建筑:{buildType}");
            }
        }
        
        public bool FindBuildingAndOpenMenu(buildtype buildType)
        {
            BuildingModule findBuild = null;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildType)
                {
                    if (buildingModule.BuildingId == 704)
                    {
                        continue;
                    }
                    findBuild = buildingModule;
                    break;
                }
            }

            if (findBuild != null)
            {
                Vector3 buildPos = findBuild.GetWorldCenterPosition();
                GameEntry.Camera.LookAtPosition(buildPos, () =>
                {
                    GameEntry.Entity.GetGameEntity(findBuild.UID)?.OnClick();
                });
                return true;
            }
            else
            {
                Debug.LogError($"找不到该类型建筑:{buildType}");
            }

            return false;
        }
        
        public void FindBuildingByLevelMin(bool? needOpen = null)
        {
            BuildingModule findBuild = null;
            m_BuildingList.Sort((a,b) => b.LEVEL.CompareTo(a.LEVEL));
            findBuild = m_BuildingList[0];
            if (findBuild != null)
            {
                Vector3 buildPos = findBuild.GetWorldCenterPosition();
                GameEntry.Camera.LookAtPosition(buildPos, () =>
                {
                    if (needOpen != null && needOpen == true)
                    {
                        GameEntry.Entity.GetGameEntity(findBuild.UID)?.OnClick();
                    }
                });
            }
        }        
        
        public void FindBuildingByLevelMax(buildtype buildType,bool? needOpenMenu = null)
        {
            BuildingModule findBuild = null;
            List<BuildingModule> findBuildList = new List<BuildingModule>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildType)
                {
                    findBuildList.Add(buildingModule);
                    break;
                }
            }
            findBuildList.Sort((a,b) => a.LEVEL.CompareTo(b.LEVEL));
            if (findBuildList.Count > 0)
            {
                findBuild = findBuildList[0];
            }
            if (findBuild != null)
            {
                Vector3 buildPos = findBuild.GetWorldCenterPosition();
                GameEntry.Camera.LookAtPosition(buildPos, () =>
                {
                    if (needOpenMenu != null && needOpenMenu == true)
                    {
                        //GameEntry.Entity.GetGameEntity(findBuild.UID)?.OnClick();
                        Entity gameEntity = GameEntry.Entity.GetGameEntity(findBuild.UID);
                        if (gameEntity is EL_Building elBuilding)
                        {
                            if (findBuild.GetBuildingState() == BuildingState.Normal)
                            {
                                elBuilding.OpenMenuLevelUp(findBuild.GetBuildingState() == BuildingState.Upgrading);
                            }
                        }
                    }
                });
            }
        }

        public BuildingModule FindBuildingById(uint buildNo)
        {
            BuildingModule findBuild = null;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.BuildingId == buildNo)
                {
                    findBuild = buildingModule;
                    break;
                }
            }

            if (findBuild != null)
            {
                return findBuild;
            }

            Debug.LogError($"找不到建筑物:{buildNo}");
            return findBuild;
        }
        
        public void FindBuildingCanBuilt()
        {
            List<build_config> buildConfigs = new List<build_config>();
            foreach (KeyValuePair<buildclass,List<build_config>> mBuildByClass in m_buildByClassDis)
            {
                buildConfigs = GetUnLockBuildConfigListByType(mBuildByClass.Key);
            }

            // 可建造
            if (buildConfigs.Count > 0)
            {
                //todo 添加箭头动画表现
                GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm, buildConfigs[0]);
                return;
            }
            else
            {
                FindBuildingByLevelMin(true);
            }
        }

        public void ChangeAllHospitalState(HospitalSoldierParams hospitalSoldierParams)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildtype.buildtype_hospital)
                {
                    buildingModule.OnHospitalQueueStateChange(hospitalSoldierParams);

                    if (hospitalSoldierParams.m_State == HospitalQueueChangeState.Working)
                    {
                        var queueModule = GameEntry.LogicData.QueueData.GetTreatQueueModule((uint)buildingModule.BuildingId);
                        if (queueModule != null && queueModule.Help == 0)
                            buildingModule.OnUnionHelpChange(QueueType.BuildSoldierTreatment, true);
                    }
                    else if (hospitalSoldierParams.m_State == HospitalQueueChangeState.TreatDone)
                    {
                        buildingModule.OnUnionHelpChange(QueueType.BuildSoldierTreatment, false);
                    }
                }
            }
        }
        
        public int GetAllHospitalCapacity()
        {
            int totalCount = 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildtype.buildtype_hospital)
                {
                    // build_hospital buildHospital = GameEntry.LDLTable.GetTableById<build_hospital>(buildingModule.LEVEL);
                    build_level buildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingModule.GetBuildingType(),buildingModule.LEVEL);
                    List<attributes> levelAttributes = buildingLevelCfg.build_level_attributes;
                    foreach (var attribute in levelAttributes)
                    {
                        totalCount += attribute.value;
                    }
                }
            }

            return totalCount;
        }

        // 消耗资源是否充足
        public bool GetResourceIsEnough(buildtype buildType,int level,out List<ItemModule>rescoureItems)
        {
            rescoureItems = new List<ItemModule>();
            int enoughCount = 0;
            build_level buildingLevelCfg = GetBuildingLevelCfg(buildType,level);
            List<cost> buildCost = buildingLevelCfg.build_cost;
            foreach (cost oneCost in buildCost)
            {
                itemid oneCostItemID = oneCost.item_id;
                long oneCostNum = oneCost.num;
                long amount = GameEntry.LogicData.BagData.GetAmountById(oneCostItemID);
                rescoureItems.Add(new ItemModule(oneCostItemID,oneCostNum));
                if (amount >= oneCostNum)
                {
                    enoughCount += 1;
                }
            }
            return enoughCount >= buildCost.Count;
        }
         // 消耗资源是否充足
        public bool GetResourceTechIsEnough(tech_config needConfig,out List<ItemModule>rescoureItems)
        {
            rescoureItems = new List<ItemModule>();
            int enoughCount = 0;
            //build_level buildingLevelCfg = GetBuildingLevelCfg(buildType,level);
            List<cost> buildCost = needConfig.tech_cost;
            foreach (cost oneCost in buildCost)
            {
                itemid oneCostItemID = oneCost.item_id;
                long oneCostNum = oneCost.num;
                long amount = GameEntry.LogicData.BagData.GetAmountById(oneCostItemID);
                rescoureItems.Add(new ItemModule(oneCostItemID,oneCostNum));
                if (amount >= oneCostNum)
                {
                    enoughCount += 1;
                }
            }
            return enoughCount >= buildCost.Count;
        }

        public bool CheckResourceIsEnough(buildtype buildType, int level)
        {
            List<ItemModule> resoureItemDictionary;
            bool isEnough = GetResourceIsEnough(buildType, level, out resoureItemDictionary);
            if (!isEnough)
            {

                GameEntry.LogicData.BagData.ResourceGetWay(resoureItemDictionary.ToArray());
            }

            return isEnough;
        }
        
        public bool CheckBuildingIsCanBuilt()
        {
            foreach (buildclass value in Enum.GetValues(typeof(buildclass)))
            {
                if (value > buildclass.buildclass_nil && value > buildclass.buildclass_season)
                {
                    List<build_config> list = GetUnLockBuildConfigListByType(value);
                    if (list.Count > 0)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        
        //根据类型获取解锁的建筑列表
        public List<build_config> GetUnLockBuildConfigListByType(buildclass selectType)
        {
            int count = 0;
            List<build_config> buildConfigs = new List<build_config>();
            List<build_config> list;
            bool isExist = m_buildByClassDis.TryGetValue(selectType,out list);
            if (isExist)
            {
                foreach (build_config buildConfig in list)
                {
                    BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById((uint)buildConfig.id);
                    bool exists = buildConfigs.Exists(b => b.build_type == buildConfig.build_type);
                    if (!exists && buildingModule == null)
                    {
                        bool isShow = false;
                        if (buildConfig?.view_demand.Count == 0)
                        {
                            isShow = true;
                        }
                        else
                        {
                            int showCount = 0;
                            for (int i = 0; i < buildConfig?.view_demand.Count; i++)
                            {
                                bool isLock = ToolScriptExtend.GetDemandUnlock(buildConfig.view_demand[i]);
                                if (isLock)
                                {
                                    showCount += 1;
                                }
                            }

                            isShow = showCount >= buildConfig?.view_demand.Count;
                        }

                        if (selectType == buildclass.buildclass_decoration)
                        {
                            var buildingLevelCfg = GetBuildingLevelCfg(buildConfig.build_type, 1);
                            if (buildingLevelCfg.build_level_attributes.Count == 0)
                            {
                                isShow = false;
                            }
                        }

                        if (isShow)
                        {
                            bool isCanBuild = false;
                            if (buildConfig.build_demand.Count == 0)
                            {
                                buildConfigs.Add(buildConfig);
                                count += 1;
                            }
                            else
                            {
                                int totalCount = 0;
                                for (int i = 0; i < buildConfig.build_demand.Count; i++)
                                {
                                    isCanBuild = ToolScriptExtend.GetDemandUnlock(buildConfig.build_demand[i]);
                                    if (isCanBuild)
                                    {
                                        totalCount += 1;
                                    }
                                }

                                if (totalCount >= buildConfig.build_demand.Count)
                                {
                                    buildConfigs.Add(buildConfig);
                                    count += 1;
                                }
                            }
                        }
                    }
                }
            }

            return buildConfigs;
        }
        
        //获取所有校场容量
        public int GetAllBuildingGroundCapacity()
        {
            int totalCapacity = 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildtype.buildtype_ground)
                {
                    build_level buildingLevelCfg = GetBuildingLevelCfg(buildtype.buildtype_ground, buildingModule.LEVEL);
                    List<attributes> buildLevelAttributes = buildingLevelCfg.build_level_attributes;
                    foreach (attributes buildLevelAttribute in buildLevelAttributes)
                    {
                        if (buildLevelAttribute.attributes_type == attributes_type.attributes_type_140 )
                        {
                            totalCapacity += buildLevelAttribute.value;
                        }
                    }
                }
            }

            return totalCapacity;
        }

        // 获取所有兵营最大解锁士兵等级
        public int GetAllBuildingSoldierMaxLevel()
        {
            int maxLevel = 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule is BuildingSoldierTraining buildingSoldier)
                {
                    if (buildingSoldier.SoldierLevel > maxLevel)
                    {
                        maxLevel = buildingSoldier.SoldierLevel;
                    }
                }
            }

            return maxLevel;
        }
        
        // 查找最大可升级等级的兵营并打开界面
        public void FindMaxLevelUpSoldierBuilding(int curUpSoldierLevel)
        {
            int maxLevel = 0;
            List<BuildingSoldierTraining> list = new List<BuildingSoldierTraining>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule is BuildingSoldierTraining buildingSoldier)
                {
                    list.Add(buildingSoldier);
                }
            }
            list.Sort((a, b) =>
            {
                if (a.SoldierLevel == b.SoldierLevel)
                {
                    return a.BuildingId.CompareTo(b.BuildingId);
                }

                return b.SoldierLevel.CompareTo(a.SoldierLevel);
            });
            BuildingModule findBuild = list[0];
            if (findBuild != null)
            {
                Vector3 buildPos = findBuild.GetWorldCenterPosition();
                GameEntry.Camera.LookAtPosition(buildPos, () =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITrainingSoldiersForm,findBuild);
                    QueueModule soldierQueueModule = GameEntry.LogicData.QueueData.GetSoldierQueueModule((uint)findBuild.BuildingId);
                    if (soldierQueueModule == null)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UISoldiersTrainUpForm,new UISoldiersTrainUpFormParam(findBuild,curUpSoldierLevel));
                    }
                });
            }
        }
        
        //通过类型获取建筑属性值
        public int GetBuildingAttrValueByType(buildtype buildingType,attributes_type attributesType)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.GetBuildingType() == buildingType)
                {
                    int attrValue = buildingModule.GetBuildingAttrValueByAttrType(attributesType);
                    return attrValue;
                }
            }
            return 0;
        }

        /// <summary>
        /// 获取扣除Buff后的升级消耗时间
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="isSub">是否扣除建筑小屋的</param>
        /// <returns></returns>
        public int GetRealBuildingTime(int time,bool isSub = true)
        {
            int realTime = time;
            // 建筑小屋的时间
            if (isSub)
            {
                int subTime = GetBuildingAttrValueByType(buildtype.buildtype_builder, attributes_type.attributes_type_55);
                realTime -= subTime;
            }
      
            realTime = Mathf.Max(0, realTime);
            return realTime;
        }
        
        public int GetRealTechdTime(int time,bool isSub = true)
        {
            int realTime = time;
            // 研究中心
            if (isSub)
            {
                int subTime = GetBuildingAttrValueByType(buildtype.buildtype_researchcenter, attributes_type.attributes_type_71);
                realTime -= subTime;
            }
      
            realTime = Mathf.Max(0, realTime);
            return realTime;
        }
        
        //改变所有校场的士兵
        public void ChangeAllGroundState(SoldierQueueChangeState state, int level, int changeNum)
        {
            bool isAdd = changeNum > 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.buildingCfg.build_type == buildtype.buildtype_ground)
                {
                    SoldierChangeType changeType = isAdd ? SoldierChangeType.Add : SoldierChangeType.Reduce;
                    buildingModule.OnSoldierQueueStateChange(new SoldierParams(state, changeType, level, changeNum));
                }
            }
        }
        
        public void ChangeSoldierState(int buildingId,int level,int changeNum)
        {
            bool isAdd = changeNum > 0;
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.BuildingId == buildingId)
                {
                    SoldierChangeType changeType = isAdd ? SoldierChangeType.Add : SoldierChangeType.Reduce;
                    buildingModule.OnSoldierQueueStateChange(new SoldierParams(SoldierQueueChangeState.SoldierChange,changeType,level,changeNum));
                }
            }
        }
        
        public void BuildingInsertSurvivor(int buildingId,uint survivorId)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.BuildingId == buildingId)
                {
                    buildingModule.AddSurvivorList(survivorId);
                }
            }
        }    
        
        public void BuildingUpdateSurvivorList(int buildingId,List<uint> survivorIds)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.BuildingId == buildingId)
                {
                    buildingModule.UpdateSurvivorList(survivorIds);
                }
            }
        }  
        
        public void AllBuildingUpdateSurvivorList()
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.DispatchAble())
                {
                    List<uint> survivorIds = GameEntry.LogicData.SurvivorData.GetSurvivorIdListByBuildId((uint)buildingModule.BuildingId);
                    buildingModule.UpdateSurvivorList(survivorIds);
                }
            }
        }  
        
        public void UpdataAllBuildingSurvivorDispatchState()
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.DispatchAble() && buildingModule.GetBuildingState() == BuildingState.Normal)
                {
                    bool canDispatch = buildingModule.CheckSurvivorDispatch();
                    if (!canDispatch)
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Remove);
                    }
                    else
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Add);
                    }
                }
            }
        }   
        
        public void UpdataAllBuildingSurvivorDispatchState(buildtype buildType)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.GetBuildingType() == buildType && buildingModule.DispatchAble() && buildingModule.GetBuildingState() == BuildingState.Normal)
                {
                    bool canDispatch = buildingModule.CheckSurvivorDispatch();
                    if (!canDispatch)
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Remove);
                    }
                    else
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Add);
                    }
                }
            }
        }

        //获取所有有派遣的建筑
        public List<buildtype> GetAllDispatchBuilding()
        {
            List<buildtype> buildingModules = new List<buildtype>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.SurvivorList.Count > 0)
                {
                    bool exists = buildingModules.Exists(d => d == buildingModule.GetBuildingType());
                    if (!exists)
                    {
                        buildingModules.Add(buildingModule.GetBuildingType());
                    }
                }
            }

            return buildingModules;
        }
        
        //刷新人才大厅建筑可升星
        public void UpdataBuildingSurvivorCanUp(bool isCanUp)
        {
            List<buildtype> buildingModules = new List<buildtype>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.GetBuildingType() == buildtype.buildtype_talenthall)
                {
                    if (isCanUp)
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Add);
                    }
                    else
                    {
                        buildingModule.OnSurvivorChange(SurvivorChangeState.Remove);
                    }
                }
            }

        }
        
        // 获取等级最低的校场
        public BuildingModule GetMinLevelBuildingModuleGround()
        {
            
            List<BuildingModule> buildingModules = new List<BuildingModule>();
            foreach (var buildingModule in m_BuildingList)
            {
                if (buildingModule.GetBuildingType() == buildtype.buildtype_ground)
                {
                    buildingModules.Add(buildingModule);
                }
            }
            buildingModules.Sort((a, b) =>
            {
                return a.LEVEL.CompareTo(b.LEVEL);
            });
            return buildingModules[0];
        }

        public bool GetTeamIsUnlock(int teamIndex)
        {
            foreach (var buildingModule in m_BuildingList)
            {
                
                if (buildingModule.GetBuildingType() == buildtype.buildtype_team)
                {
                    int buildingTeamIndex = buildingModule.BuildingId % 100;
                    if (buildingTeamIndex == teamIndex)
                    {
                        if (buildingTeamIndex != 4)
                        {
                            return true;
                        }
                        else
                        {
                            bool unlock = ToolScriptExtend.GetDemandUnlock(buildingModule.buildingCfg.build_demand[0]);
                            return unlock;
                        }
                    }
                }
            }
            return false;
        }
        
        #region 协议相关

        /// <summary>
        /// 建造请求
        /// </summary>
        /// <param name="build"></param>
        /// <param name="createType">1:普通建造 2:钻石建造</param>
        /// <param name="callBack"></param>
        public void BuildCreateResp(Build.Build build,Action<BuildQueueResult> callBack = null)// 1:普通建造 2:钻石建造
        {
            BuildCreateReq buildCreateReq = new BuildCreateReq();
            buildCreateReq.Build = build;
            if (!TempConnectHelper.Instance.IsOffline())
            {
                GameEntry.LDLNet.Send(Protocol.MessageID.BuildCreate, buildCreateReq, (message) =>
                {
                    BuildCreateResp resp = (BuildCreateResp)message;
                    if (resp != null)
                    {
                        HandleBuildQueueResult(resp.Result);
                        callBack?.Invoke(resp.Result);
                    }
                });
            }
            else
            {
                //测试数据
                var queue = new Build.Queue();
                queue.QueueUID = (uint)Game.GameEntry.Entity.GenerateSerialId();
                queue.QueueType = 1;
                queue.BuildNo = build.BuildNo;
                queue.Help = 0;
                queue.CreateTime = (ulong)(TimeComponent.Now);
                queue.FinishTime = (ulong)(TimeComponent.Now + 10);
                queue.AccelerateTime = 0;
                
                var buildQueueResult = new BuildQueueResult();
                buildQueueResult.Build = build;
                buildQueueResult.Queue = queue;
                
                BuildCreateResp resp = new BuildCreateResp();
                resp.Result = buildQueueResult;
                
                HandleBuildQueueResult(resp.Result);
                callBack?.Invoke(resp.Result);
            }
        }

        /// <summary>
        /// 建筑物升级
        /// </summary>
        /// <param name="buildNo"></param>
        /// <param name="upgradeType">1:普通升级 2:钻石升级</param>
        /// <param name="callBack"></param>
        public void BuildUpgradeReq(uint buildNo,UpgradeType upgradeType,Action<Build.Build> callBack = null)
        {
            BuildUpgradeReq buildUpgradeReq = new BuildUpgradeReq();
            buildUpgradeReq.BuildNo = buildNo;
            buildUpgradeReq.UpgradeType = upgradeType;
            if (!TempConnectHelper.Instance.IsOffline())
            {
                GameEntry.LDLNet.Send(Protocol.MessageID.BuildUpgrade, buildUpgradeReq, (message) =>
                {
                    BuildUpgradeResp resp = (BuildUpgradeResp)message;
                    if (resp != null)
                    {
                        HandleBuildQueueResult(resp.Result);
                        if(resp.Result!=null)
                            callBack?.Invoke(resp.Result.Build);

                        if (resp.Result!=null && resp.Result.Build != null)
                        {
                            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(resp.Result.Build.BuildNo);
                            if (buildingModule is BuildingSoldierTraining soldierTraining)
                            {
                                soldierTraining.UpdateSoldierUnLockLevel();
                            }
                        }
                    }
                });
            }
            else
            {
                //测试数据
                var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(buildNo);
                var queue = new Build.Queue();
                queue.QueueUID = (uint)Game.GameEntry.Entity.GenerateSerialId();
                queue.QueueType = 2;
                queue.BuildNo = (uint)buildingModule.BuildingId;
                queue.Help = 0;
                queue.CreateTime = (ulong)(TimeComponent.Now);
                queue.FinishTime = (ulong)(TimeComponent.Now + 10);
                queue.AccelerateTime = 0;
                
                var buildQueueResult = new BuildQueueResult();
                buildQueueResult.Build = buildingModule.ToBuild();
                buildQueueResult.Build.BuildLevel += 1;
                buildQueueResult.Queue = queue;
                
                BuildCreateResp resp = new BuildCreateResp();
                resp.Result = buildQueueResult;
                
                HandleBuildQueueResult(resp.Result);
                callBack?.Invoke(resp.Result.Build);
            }
        }
        
        /// <summary>
        /// 建筑物移动
        /// </summary>
        /// <param name="build"></param>
        /// <param name="callBack"></param>
        public void BuildMoveReq(Build.Build build, Action<Build.Build> callBack = null)
        {
            BuildMoveReq req = new BuildMoveReq();
            req.Build = build;
            if (!TempConnectHelper.Instance.IsOffline())
            {
                GameEntry.LDLNet.Send(Protocol.MessageID.BuildMove, req, (message) =>
                {
                    BuildMoveResp resp = (BuildMoveResp)message;
                    if (resp != null)
                    {
                        HandleBuildQueueResult(resp.Build, null, 0);
                        callBack?.Invoke(resp.Build);
                    }
                });
            }
            else
            {
                //测试数据
                HandleBuildQueueResult(build, null, null);
                callBack?.Invoke(build);
            }
        }

        
        /// <summary>
        /// 请求队列完成
        /// </summary>
        /// <param name="buildNo"></param>
        /// <param name="queueUid"></param>
        /// <param name="callBack"></param>
        public void BuildQueueFinishReq(uint buildNo, uint queueUid, Action<BuildQueueResult> callBack = null)
        {
            BuildQueueFinishReq req = new BuildQueueFinishReq();
            req.BuildNo = buildNo;
            req.QueueUID = queueUid;
            if (!TempConnectHelper.Instance.IsOffline())
            {
                GameEntry.LDLNet.Send(Protocol.MessageID.BuildQueueFinish, req, (message) =>
                {
                    BuildQueueFinishResp resp = (BuildQueueFinishResp)message;
                    if (resp != null)
                    {
                        HandleBuildQueueResult(resp.Result);
                        callBack?.Invoke(resp.Result);
                    }
                });
            }
            else
            {
                var buildingModule = GetBuildingModuleById(buildNo);
                var build = buildingModule.ToBuild();
                HandleBuildQueueResult(build, null, queueUid);
                //callBack?.Invoke(build);
            }
        }

        /// <summary>
        /// 队列加速
        /// </summary>
        /// <param name="buildNo"></param>
        /// <param name="queueUid"></param>
        /// <param name="articles"></param>
        /// <param name="callBack"></param>
        /// <param name="accelerateType"></param>
        public void BuildQueueAccelerateReq(uint buildNo,uint queueUid,List<Article.Article> articles,AccelerateType accelerateType,Action<BuildQueueAccelerateResp> callBack = null)
        {
            BuildQueueAccelerateReq req = new BuildQueueAccelerateReq();
            req.BuildNo = buildNo;
            req.QueueUID = queueUid;
            req.Articles.AddRange(articles);
            req.AccelerateType = accelerateType;
            GameEntry.LDLNet.Send(Protocol.MessageID.BuildQueueAccelerate,req, (message) =>
            {
                BuildQueueAccelerateResp resp = (BuildQueueAccelerateResp)message;
                if (resp!=null)
                {
                    GameEntry.LogicData.QueueData.AccelerateQueue(resp.QueueUID, resp.AccelerateTime);
                    callBack?.Invoke(resp);
                }
            });
            // GameEntry.LogicData.QueueData.AccelerateQueue(queueUid, 30);
        }

        /// <summary>
        /// 请求队列帮助
        /// </summary>
        /// <param name="buildNo"></param>
        /// <param name="queueUid"></param>
        /// <param name="callBack"></param>
        public void BuildQueueHelpReq(uint buildNo,uint queueUid,Action<Build.BuildQueueHelpResp> callBack = null)
        {
            BuildQueueHelpReq req = new BuildQueueHelpReq();
            req.BuildNo = buildNo;
            req.QueueUID = queueUid;
            GameEntry.LDLNet.Send(Protocol.MessageID.BuildQueueHelp,req, (message) =>
            {
                BuildQueueHelpResp resp = (BuildQueueHelpResp)message;
                if (resp != null)
                {
                    var queueModule = GameEntry.LogicData.QueueData.GetQueueModuleByQueueID(resp.QueueUID);
                    if (queueModule != null)
                    {
                        queueModule.Help = 1;
                    }
                    callBack?.Invoke(resp);
                }
            });
        }
        
        /// <summary>
        /// 请求租赁建造队列
        /// </summary>
        /// <param name="index"></param>
        /// <param name="callBack"></param>
        public void BuildQueueRentingReq(uint index,Action callBack = null)
        {
            WorkerRentingReq req = new WorkerRentingReq();
            req.Index = index;
            GameEntry.LDLNet.Send(Protocol.MessageID.WorkerRenting,req, (message) =>
            {
                WorkerRentingResp resp = (WorkerRentingResp)message;
                if (resp != null)
                {
                    HandleBuildQueueRentingResult(resp.Worker);
                    callBack?.Invoke();
                }
            });
        }
        
        /// <summary>
        /// 请求操作士兵(训练,晋级)
        /// </summary>
        /// <param name="buildId"></param>
        /// <param name="soldier"></param>
        /// <param name="soldierOpType"></param>
        /// <param name="articles"></param>
        /// <param name="callBack"></param>
        public void SoldierOpReq(uint buildId,Build.Soldier soldier,SoldierOpType soldierOpType,SoldierCostType costType,List<Article.Article> articles,Action callBack = null)
        {
            SoldierOpReq req = new SoldierOpReq();
            req.BuildNo = buildId;
            req.Soldier = soldier;
            req.OpType = soldierOpType;
            req.SoldierCostType = costType;
            req.Articles.AddRange(articles);
            
            GameEntry.LDLNet.Send(Protocol.MessageID.SoldierOp,req, (message) =>
            {
                SoldierOpResp resp = (SoldierOpResp)message;
                if (resp != null)
                {
                    HandleBuildQueueResult(resp.Result);
                    callBack?.Invoke();
                }
            });
        }
        
        /// <summary>
        /// 请求治疗士兵
        /// </summary>
        /// <param name="soldier"></param>
        /// <param name="soldierOpType"></param>
        /// <param name="articles"></param>
        /// <param name="callBack"></param>
        public void SoldierTreatmentReq(List<Build.Soldier> soldier,SoldierCostType costType,List<Article.Article> articles,Action callBack = null)
        {
            SoldierTreatmentReq req = new ();
            req.Soldiers.AddRange(soldier);
            req.Articles.AddRange(articles);
            req.SoldierCostType = costType;
            
            GameEntry.LDLNet.Send(Protocol.MessageID.SoldierTreatment,req, (message) =>
            {
                SoldierTreatmentResp resp = (SoldierTreatmentResp)message;
                if (resp != null)
                {
                    HandleBuildQueueResult(resp.Result);
                    callBack?.Invoke();
                }
            });
        }
        
        private void HandleBuildQueueRentingResult(Worker.Worker buildQueueResult)
        {
            if (buildQueueResult != null)
            {
                GameEntry.LogicData.QueueData.AddBuildQueue(buildQueueResult);
            }
        }

        public void HandleBuildQueueResult(BuildQueueResult buildQueueResult)
        {
            HandleBuildQueueResult(buildQueueResult?.Build, buildQueueResult?.Queue, buildQueueResult?.QueueUID);
        }
        #endregion
        
        private void HandleBuildQueueResult(Build.Build build,Build.Queue queue = null,uint? finishQueueUid = null)
        {
            var queueData = GameEntry.LogicData.QueueData;
            
            if (build != null)
            {
                AddOrUpdateBuilding(build);
            }
            
            //先移除完成的队列
            if (finishQueueUid != null)
            {
                queueData.RemoveQueue(finishQueueUid.Value);
            }
            
            if (queue != null)
            {
                queueData.AddQueue(queue);
            }
        }
        
        private void AddOrUpdateBuilding(Build.Build build)
        {
            var buildingModule = GetBuildingModuleById(build.BuildNo);
            if (buildingModule == null)
            {
                //创建
                buildingModule = CreatBuildingMoudleByType(build);
                m_BuildingList.Add(buildingModule);
                m_BuildingDic.Add(buildingModule.UID, buildingModule);
                GameEntry.LogicData.GridData.AddBuilding(buildingModule);

                Game.GameEntry.Event.Fire(this, OnNewBuildingCreateEventArgs.Create(buildingModule));
            }
            else
            {
                buildingModule.UpdateInfo(build);
            }
        }

        
        public IEnumerator<BuildingModule> GetEnumerator()
        {
            foreach (BuildingModule item in m_BuildingList)
            {
                yield return item;
            }
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}