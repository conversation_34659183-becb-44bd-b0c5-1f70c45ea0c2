using System.Collections.Generic;
using Game.Hotfix;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroBattleSKill
    {
        public int SkillId => m_SkillConfig?.id ?? 0;
        public int ShowId => m_SkillShowConfig.id;
        public bool IsMainSkill => m_SkillConfig?.skill_type == skilltpye.skilltype_2;
        public BattleHero Caster => m_Caster;
        public BattleFiled BattleFiled => m_BattleFiled;
        public skill_show ShowConfig => m_SkillShowConfig;
        public skill_config SkillConfig => m_SkillConfig;
        public bool IsCasting => m_IsCasting;
        public List<EnumBattlePos> TargetList => m_TargetList;
        public List<SkillTargetData> FinalTargets => m_FinalTargets;

        private BattleFiled m_BattleFiled;
        private BattleHero m_Caster;
        private List<EnumBattlePos> m_TargetList;
        private List<SkillTargetData> m_FinalTargets;
        
        private skill_show m_SkillShowConfig;
        private bool m_IsCasting = false;
        private float m_SkillCurDuration;
        private float m_SkillTotalDuration;

        private float m_SkillCurCD;
        private float m_SkillTotalCD;

        private skill_config m_SkillConfig;

        private HeroSkillActionAnimation m_ActionAnimation;
        private HeroSkillActionParticle m_ActionParticle;
        private HeroSkillActionBullet m_ActionBullet;

        public HeroBattleSKill(BattleFiled battleFiled, skill_show skillShowConfig, BattleHero caster,skill_config skillConfig)
        {
            m_BattleFiled = battleFiled;
            m_SkillShowConfig = skillShowConfig;
            m_Caster = caster;
            m_SkillConfig = skillConfig;

            if (skillConfig != null)
            {
                m_SkillTotalCD = skillConfig.cd_time / 1000f;
                m_SkillCurCD = m_SkillTotalCD;
            }else{
                m_SkillTotalCD = 0;
                m_SkillCurCD = 0;
            }

            m_SkillTotalDuration = m_SkillShowConfig.duration;
            m_SkillCurDuration = m_SkillTotalDuration;

            m_ActionAnimation =
                new HeroSkillActionAnimation(this, m_SkillShowConfig.a_start_time, m_SkillShowConfig.a_name);
            m_ActionParticle = new HeroSkillActionParticle(this, m_SkillShowConfig.e_start_time,
                m_SkillShowConfig.e_effect_id,
                m_SkillShowConfig.e_slot);
            m_ActionBullet = new HeroSkillActionBullet(this, m_SkillShowConfig.b_start_time);
        }

        public void GetSkillCD(out float curCD,out float totalCD)
        {
            curCD = m_SkillCurCD;
            totalCD = m_SkillTotalCD;
        }
        
        /// <summary>
        /// 技能释放的时候调用
        /// </summary>
        /// <param name="targetList"></param>
        public void OnCast(List<EnumBattlePos> targetList)
        {
            m_TargetList = targetList;
            FindFinalTargets(targetList);
            m_SkillCurDuration = 0;
            m_SkillCurCD = 0;
            m_IsCasting = true;

            m_ActionAnimation?.Begin();
            m_ActionParticle?.Begin();
            m_ActionBullet?.Begin();
        }

        public void OnTick(float dt)
        {
            if (m_SkillCurCD < m_SkillTotalCD)
            {
                m_SkillCurCD += dt; 
                if (m_SkillCurCD > m_SkillTotalCD)
                    m_SkillCurCD = m_SkillTotalCD;
            }
            
            m_ActionAnimation?.Tick(dt);
            m_ActionParticle?.Tick(dt);
            m_ActionBullet?.Tick(dt);
            
            if (!m_IsCasting) return;

            m_SkillCurDuration += dt; 
            

            if (m_SkillCurDuration > m_SkillTotalDuration)
            {
                OnSkillFinish();
            }
        }

        private void OnSkillFinish()
        {
            m_IsCasting = false;
        }

        private void FindFinalTargets(List<EnumBattlePos> targetList)
        {
            // 目标具体定位
            target_type targetType = ShowConfig.b_target_type;
            m_FinalTargets = new List<SkillTargetData>();
            for (int i = 0; i < targetList.Count; i++)
            {
                SkillTargetData targetData = GetFinalTarget(targetList[i], targetType);
                m_FinalTargets.Add(targetData);
            }
        }
        
        /// <summary>
        /// 获得目标
        /// </summary>
        /// <param name="teamUid"></param>
        /// <param name="targetType"></param>
        /// <returns></returns>
        private SkillTargetData GetFinalTarget(EnumBattlePos teamUid, target_type targetType)
        {
            var battleHero = m_BattleFiled.TeamCtrl.GetBattleHero(teamUid);
            SkillTargetData targetData = new SkillTargetData();
            if (targetType == target_type.unit)
            {
                targetData.Target = battleHero;
                targetData.TargetType = targetType;
                targetData.TargetPos = battleHero.GetPosition();
                
                var targetSlotPos = battleHero.GetSlotPosition(m_SkillShowConfig.b_hurt_slots);
                if (targetSlotPos != Vector3.zero)
                    targetData.TargetPos = targetSlotPos;
            }
            else if (targetType == target_type.enemy_front_line)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosR1, EnumBattlePos.PosR2);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosL1, EnumBattlePos.PosL2);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }
            else if (targetType == target_type.enemy_back_line)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosR3, EnumBattlePos.PosR4, EnumBattlePos.PosR5);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosL3, EnumBattlePos.PosL4, EnumBattlePos.PosL5);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }
            else if (targetType == target_type.enemy_mid_field)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosR1, EnumBattlePos.PosR2, EnumBattlePos.PosR3,
                        EnumBattlePos.PosR4, EnumBattlePos.PosR5);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosL1, EnumBattlePos.PosL2, EnumBattlePos.PosL3,
                        EnumBattlePos.PosL4, EnumBattlePos.PosL5);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }
            else if (targetType == target_type.friendly_front_line)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosL1, EnumBattlePos.PosL2);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosR1, EnumBattlePos.PosR2);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }
            else if (targetType == target_type.friendly_back_line)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosL3, EnumBattlePos.PosL4, EnumBattlePos.PosL5);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosR3, EnumBattlePos.PosR4, EnumBattlePos.PosR5);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }
            else if (targetType == target_type.friendly_mid_field)
            {
                var side = m_Caster.GetSide();
                Vector3 position;
                if (side == EnumBattleSide.Left)
                    position = GetAverageTeamPosition(EnumBattlePos.PosL1, EnumBattlePos.PosL2, EnumBattlePos.PosL3,
                        EnumBattlePos.PosL4, EnumBattlePos.PosL5);
                else
                    position = GetAverageTeamPosition(EnumBattlePos.PosR1, EnumBattlePos.PosR2, EnumBattlePos.PosR3,
                        EnumBattlePos.PosR4, EnumBattlePos.PosR5);
                targetData.Target = null;
                targetData.TargetType = targetType;
                targetData.TargetPos = position;
            }

            return targetData;
        }
        
        private Vector3 GetAverageTeamPosition(params EnumBattlePos[] teamIds)
        {
            Vector3 pos = Vector3.zero;
            for (int i = 0; i < teamIds.Length; i++)
            {
                var tempPos = m_BattleFiled.GetTeamPosByUid(teamIds[i]);
                pos += tempPos;
            }

            if (teamIds.Length > 0)
                pos /= teamIds.Length;
            return pos;
        }

        public SkillTargetData GetFirstTarget()
        {
            if (m_FinalTargets != null && m_FinalTargets.Count >= 0)
            {
                return m_FinalTargets[0];
            }

            return null;
        }

        public void ResetCD()
        {
            m_SkillCurCD = 0;
        }

    }
    
}