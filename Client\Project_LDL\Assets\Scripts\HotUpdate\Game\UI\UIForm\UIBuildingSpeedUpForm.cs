using System;
using System.Collections;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class OpenSpeedUpParam
    {
        public BuildingModule m_BuildingModule;
        public itemsubtype m_itemSubType;
        public itemid m_curItemId;
        public int m_techBuildId;
        public OpenSpeedUpParam(BuildingModule b, itemsubtype itemsubtype, itemid itemId = itemid.itemid_nil, int techBuildId = 0)
        {
            m_BuildingModule = b;
            m_itemSubType = itemsubtype;
            m_curItemId = itemId;
            m_techBuildId = techBuildId;
        }
    }
    public partial class UIBuildingSpeedUpForm : UGuiFormEx
    {
        private BuildingModule m_curBuildingModule;
        private float delaTime = 0;
        private QueueModule queue;
        private Dictionary<itemid,ItemModule> itemList;
        private List<long> itemIdList = new List<long>();
        private itemsubtype curUseItemSubtype = itemsubtype.itemsubtype_nil;
        private Dictionary<int, UIItemModule> itemObjList;
        private itemid euqItemid = itemid.itemid_nil;      
        private int techBuildId = 0;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            m_TableViewVAttr.GetItemCount = () => itemIdList.Count;
            m_TableViewVAttr.GetItemGo = () => m_goSpeedUpItem;
            m_TableViewVAttr.UpdateItemCell = UpdataAttrListView;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            OpenSpeedUpParam openSpeedUpParam = userData as OpenSpeedUpParam;
            if (openSpeedUpParam == null)
            {
                return;
            }

            itemObjList = new Dictionary<int, UIItemModule>();
            m_curBuildingModule = openSpeedUpParam.m_BuildingModule;
            curUseItemSubtype = openSpeedUpParam.m_itemSubType;
            euqItemid = openSpeedUpParam.m_curItemId;
            techBuildId = openSpeedUpParam.m_techBuildId;
            ResetUI();
            ResetSpeedUpItemListView();
            ResetGiftpackInfo();
            GameEntry.Event.Subscribe(PaymentFinishEventArgs.EventId, OnPaymentFinish);
        }

        protected void ResetUI()
        {
            m_imgBuildingIcon.SetImage(m_curBuildingModule.BuildingIcon);
            uint BuildingId = (uint)m_curBuildingModule.BuildingId;
            if (curUseItemSubtype == itemsubtype.itemsubtype_buildspeedup)
            {
                queue = GameEntry.LogicData.QueueData.GetBuildQueueModule(BuildingId);
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_healspeedup)
            {
                queue = GameEntry.LogicData.QueueData.GetTreatQueueModule(BuildingId);
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_trainspeedup)
            {
                queue = GameEntry.LogicData.QueueData.GetSoldierTrainQueueModule(BuildingId);
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_generalspeedup)
            {
                queue = GameEntry.LogicData.QueueData.GetEquipmentQueueModule(BuildingId);
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_researchspeedup)
            {
                //改
                if (techBuildId > 0)
                {
                    switch (techBuildId)
                    {
                        case 2201:
                            queue = GameEntry.LogicData.TechData.GetTechQueueLine1();
                            break;
                        case 2202:
                            queue = GameEntry.LogicData.TechData.GetTechQueueLine2();
                            break;
                        case 2203:
                            queue = GameEntry.LogicData.TechData.GetTechQueueLine3();
                            break;
                        default:
                            queue = GameEntry.LogicData.TechData.GetTechQueueLine1();
                            break;
                    }
                }
            }
            if (queue == null)
            {
                Debug.LogError($"列队为空!");
                return;
            }

            var remainTime = queue.GetRemainTime();
            var totalTime = queue.GetTotalTime();
            m_sliderSpeedUp.value = (1 - remainTime) / totalTime;

            // todo 装备工厂不显示建筑图标，不显示帮助
            if (m_curBuildingModule.GetBuildingType() == buildtype.buildtype_equipfactory)
            {
                m_goHelp.SetActive(false);
                m_imgBuildingIcon.gameObject.SetActive(false);
                m_imgEquipIcon.gameObject.SetActive(true);
                if (euqItemid != itemid.itemid_nil)
                {
                    string itemIcon = ToolScriptExtend.GetItemIcon(euqItemid);
                    m_imgEquipIcon.SetImage(itemIcon);
                }
            }
            else
            {
                var isShowHelp = queue.Help == 1;
                if (isShowHelp)
                {
                    var unionHelpData = GameEntry.LogicData.UnionData.GetHelpData((int)queue.QueueUid);
                    isShowHelp = unionHelpData != null;
                    if (isShowHelp)
                    {
                        m_txtHelpNum.text = $"{unionHelpData.HelpTimes}/{unionHelpData.MaxHelpTimes}";
                    }
                }
                m_goHelp.SetActive(isShowHelp);
            }
        }        
        
        protected void ResetSpeedUpItemListView()
        {
            itemList = GetSpeedUpItemList(out itemIdList);
            m_TableViewVAttr.InitTableViewByIndex(0);
        }

        protected Dictionary<itemid,ItemModule> GetSpeedUpItemList([CanBeNull] out List<long> idList)
        {
            idList = new List<long>();
            
            Dictionary<itemid,ItemModule> list = new Dictionary<itemid,ItemModule>();
            List<itemsubtype> itemTypeList = new List<itemsubtype>();
            itemTypeList.Add(curUseItemSubtype);
            itemTypeList.Add(itemsubtype.itemsubtype_generalspeedup);
            
            foreach (itemsubtype itemSubType in itemTypeList)
            {
                var itemModules = GameEntry.LogicData.BagData.GetDataBySubType(itemSubType);
                foreach (ItemModule itemModule in itemModules)
                {
                    list[itemModule.ItemId] = itemModule;
                    idList.Add((long)itemModule.ItemId);
                }
            }
            idList.Insert(0,(long)-2);
            if (list.Count > 0)
            {
                idList.Insert(0,(long)-1);
            }
            
            return list;
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            if (delaTime < 1)
            {
                delaTime += elapseSeconds;
                return;
            }

            if (queue == null)
            {
                return;
            }
            
            var remainTime = queue.GetRemainTime();
            int leftTime = Mathf.FloorToInt(remainTime);
            if (leftTime <= 0)
            {
                m_txtSpeedUpTime.text = "0";
                Close(true);
                return;
            }
            string dateUtcTimeText = TimeHelper.FormatGameTimeWithDays(leftTime);
            m_txtSpeedUpTime.text = dateUtcTimeText;
            var totalTime = queue.GetTotalTime();
            m_sliderSpeedUp.value = (1 - remainTime) / totalTime;
        }
        
        protected void UpdataAttrListView(int index, GameObject obj)
        {
            Text txtItemName           = obj.transform.Find("txtItemName").GetComponent<Text>();
            Text txtItemDesc           = obj.transform.Find("txtItemDesc").GetComponent<Text>();
            
            Transform transItem        = obj.transform.Find("transItem").GetComponent<Transform>();
            Transform itemTransItem    = obj.transform.Find("itemTransItem").GetComponent<Transform>();
            GameObject oneKeyUpItem    = obj.transform.Find("transItem/oneKeyUpItem").gameObject;


            UIButton btnBuy            = obj.transform.Find("btnBuy").GetComponent<UIButton>();
            Text txtBuy                = obj.transform.Find("btnBuy/txtBuy").GetComponent<Text>();
            Text txtBuyValue           = obj.transform.Find("btnBuy/txtBuyValue").GetComponent<Text>();
            UIButton btnOnClickUse     = obj.transform.Find("btnOnClickUse").GetComponent<UIButton>();
            Text txtOnClickUse         = obj.transform.Find("btnOnClickUse/txtOnClickUse").GetComponent<Text>();
            UIButton btnUse            = obj.transform.Find("btnUse").GetComponent<UIButton>();
            Text txtUse                = obj.transform.Find("btnUse/txtUse").GetComponent<Text>();

            oneKeyUpItem.SetActive(false);
            btnBuy.gameObject.SetActive(false);
            btnOnClickUse.gameObject.SetActive(false);
            btnUse.gameObject.SetActive(false);
            itemTransItem.gameObject.SetActive(false);
            transItem.gameObject.SetActive(false);
            txtUse.text = ToolScriptExtend.GetLang(1100001);
            
            List<Article.Article> articles = new List<Article.Article>();
            var id = itemIdList[index];
            switch (id)
            {
                case -1 :
                    transItem.gameObject.SetActive(true);
                    oneKeyUpItem.SetActive(true);
                    btnOnClickUse.gameObject.SetActive(true);
                    txtItemName.text = ToolScriptExtend.GetLang(1100059);
                    txtItemDesc.text = ToolScriptExtend.GetLang(1100060);
                    txtOnClickUse.text = ToolScriptExtend.GetLang(1100059);
                    btnOnClickUse.onClick.RemoveAllListeners();
                    btnOnClickUse.onClick.AddListener(() =>
                    {
                        var remainTime = queue.GetRemainTime();
                        if (remainTime > 0)
                        {
                            if (techBuildId > 0)
                            {
                                BuildingModule techBuildModule = GameEntry.LogicData.BuildingData.FindBuildingById((uint)techBuildId);
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIUseSpeedUpItemForm, new UseSpeedUpItemParam(techBuildModule, queue, curUseItemSubtype));
                            }
                            else
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIUseSpeedUpItemForm,new UseSpeedUpItemParam(m_curBuildingModule,queue,curUseItemSubtype));
                            }
                            
                        }
                    });
                    break;
                case -2 :
                    articles.Clear();
                    transItem.gameObject.SetActive(true);
                    oneKeyUpItem.SetActive(true);
                    btnBuy.gameObject.SetActive(true);
                    txtItemName.text = ToolScriptExtend.GetLang(1100056);
                    txtBuy.text = ToolScriptExtend.GetLang(1100057);
                    txtItemDesc.text = ToolScriptExtend.GetLang(1100060);
                    int time = (int)queue.GetRemainTime();//m_curBuildingModule.GetBuildingLevelCfg().time;
                    int costDiamond = GameEntry.LogicData.BuildingData.GetCostDiamond(time);
                    txtBuyValue.text = costDiamond.ToString();
                    btnBuy.onClick.RemoveAllListeners();
                    btnBuy.onClick.AddListener(() =>
                    {
                        if (techBuildId > 0)
                        {
                            BuildingModule techBuildModule = GameEntry.LogicData.BuildingData.FindBuildingById((uint)techBuildId);
                            GameEntry.LogicData.BuildingData.BuildQueueAccelerateReq((uint)techBuildModule.BuildingId, queue.QueueUid, articles, AccelerateType.AccelerateDiamond,
                            (rep) =>
                            {
                                Close(true);
                            });
                        }
                        else
                        {
                          GameEntry.LogicData.BuildingData.BuildQueueAccelerateReq((uint)m_curBuildingModule.BuildingId,queue.QueueUid,articles,AccelerateType.AccelerateDiamond,
                            (rep) =>
                            {
                                Close(true);
                            });  
                        }
                        
                    });
                    break;
                default:
                    btnUse.gameObject.SetActive(true);
                    itemTransItem.gameObject.SetActive(true);
                    itemid itemId = (itemid)id;
                    ItemModule itemModule = itemList[itemId];
                    UIItemModule itemObj;
                    bool containsKey = itemObjList.TryGetValue(index,out itemObj);
                    if (!containsKey)
                    {
                        BagManager.CreatItem(itemTransItem, itemId, itemModule.Count, (item)=>
                        {
                            item.transform.localScale = new Vector3(0.6f, 0.6f, 1f);
                            item.GetComponent<UIButton>().useTween = false;
                            // item.SetClick(()=>
                            // {
                            //     
                            // });
                            itemObjList[index] = item;
                            txtItemName.text = item.Name;
                            txtItemDesc.text = item.Description;
                            btnUse.gameObject.SetActive(true);
                        });
                    }
                    else
                    {
                        txtItemName.text = itemObj.Name;
                        txtItemDesc.text = itemObj.Description;
                        btnUse.gameObject.SetActive(true);
                    }
                    
                    btnUse.onClick.RemoveAllListeners();
                    btnUse.onClick.AddListener(() =>
                    {
                        articles.Clear();
                        Article.Article article = new Article.Article();
                        article.Code = (PbGameconfig.itemid)itemId;
                        article.Amount = 1;
                        articles.Add(article);
                        if (articles.Count > 0)
                        {
                            GameEntry.LogicData.BuildingData.BuildQueueAccelerateReq((uint)m_curBuildingModule.BuildingId,queue.QueueUid,articles,AccelerateType.AccelerateItem);
                        }
                    });
                    break;
            }
        }  

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            curUseItemSubtype = itemsubtype.itemsubtype_nil;
            itemIdList = null;
            itemList = null;
            m_curBuildingModule = null;
            curUseItemSubtype = itemsubtype.itemsubtype_nil;
            queue = null;
            itemObjList = null;
            m_scrollviewItemList.content.gameObject.DestroyAllChild();
            GameEntry.Event.Unsubscribe(PaymentFinishEventArgs.EventId, OnPaymentFinish);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnPaymentFinish(object sender, GameEventArgs e)
        {
            ResetGiftpackInfo();
        }

        private int GetGiftId()
        {
            if (m_curBuildingModule.GetBuildingType() == buildtype.buildtype_equipfactory)
            {
                return 0;
            }
            int globalId = 0;
            if (curUseItemSubtype == itemsubtype.itemsubtype_buildspeedup)
            {
                globalId = 70;
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_healspeedup)
            {
                globalId = 71;
            }
            else if (curUseItemSubtype == itemsubtype.itemsubtype_trainspeedup)
            {
                globalId = 72;
            }

            global_setting globalSetting = GameEntry.LDLTable.GetTableById<global_setting>(globalId);
            if (globalSetting != null)
            {
                List<string> settingValue = globalSetting.value;
                globalId = int.Parse(settingValue[0]);
            }

            return globalId;
        }
        
        private void ResetGiftpackInfo()
        {
            int giftId = GetGiftId();
            m_goGift.gameObject.SetActive(giftId > 0);
            RectTransform itemListRect = m_TableViewVAttr.GetComponent<RectTransform>();
            if (giftId > 0)
            {
                itemListRect.sizeDelta = new Vector2(1020, 688);
            }
            else
            {
                itemListRect.sizeDelta = new Vector2(1020, 918);
            }
            if (giftId != 0)
            {
                GameEntry.LogicData.MallData.C2SGiftGetWay((int)giftId, (resultId) =>
                {
                    RefreshGiftInfo(resultId);
                });
            }
     
        }

        private void RefreshGiftInfo(int giftId)
        {
            int rewardCount = 0;
            if (giftId > 0)
            {
                var rewardList = GameEntry.LogicData.MallData.GetRewardList(giftId);
                rewardCount = rewardList.Count;
                
                // 设置礼包信息
                if (m_txtGiftDesc != null)
                {
                    var giftConfig = GameEntry.LDLTable.GetTableById<gift_pack>(giftId);
                    if (giftConfig != null)
                    {
                        m_txtGiftDesc.text = ToolScriptExtend.GetLang(giftConfig.gift_pack_name);
                        
                        // 设置礼包图标
                        if (m_imgGiftIcon != null)
                        {
                            m_imgGiftIcon.SetImage(giftConfig.gift_pack_icon);
                        }
                
                        // 设置礼包价格
                        if (m_txtGiftBuy != null && m_btnGiftBuy != null)
                        {
                            var paymentId = (int)giftConfig.payment_id;
                            var paymentConfig = GameEntry.LDLTable.GetTableById<payment>(paymentId);
                            if (paymentConfig != null)
                            {
                                m_txtGiftBuy.text = GameEntry.LogicData.MallData.GetPrice(giftConfig.payment_id);
                            }
                            GameEntry.LogicData.MallData.CreateRechargeScore(giftConfig.payment_id, m_btnGiftBuy.gameObject.transform);
                
                            BindBtnLogic(m_btnGiftBuy, () =>
                            {
                                GameEntry.PaymentData.Pay(giftConfig.payment_id);
                            });
                        
                            m_txtDiscount.text = $"{giftConfig.cost_effectiveness}% {ToolScriptExtend.GetLang(1100286)}";
                        }
                    }
                }

                RectTransform giftContent = m_scrollviewItemList.content;
                // 设置礼包奖励列表
                if (giftContent != null)
                {
                    // 先隐藏所有奖励物品
                    for (int i = 0; i < giftContent.transform.childCount; i++)
                    {
                        giftContent.transform.GetChild(i).gameObject.SetActive(false);
                    }
                    
                    // 显示需要的奖励物品
                    for (int i = 0; i < rewardCount; i++)
                    {
                        GameObject itemObj = null;
                        if ( giftContent.transform.childCount <= i)
                        {
                            BagManager.CreatItem(giftContent, rewardList[i].item_id, rewardList[i].num, (item)=>
                            {
                                // item.transform.localScale = new Vector3(0.45f, 0.45f, 1f);
                                itemObj = item.gameObject;
                                item.GetComponent<UIButton>().useTween = false;
                                item.SetClick(()=> item.OpenTips());
                            });
                        }
                        else
                        {
                            itemObj = giftContent.transform.GetChild(i).gameObject;
                            UIItemModule itemModule = itemObj.GetComponent<UIItemModule>();
                            // 设置奖励物品数据
                            itemModule = itemObj.AddComponent<UIItemModule>();
                            itemModule.SetData(rewardList[i].item_id, rewardList[i].num);
                            itemModule.DisplayInfo();
                        }
                    }
                }
            }
            
            // 显示或隐藏整个礼包面板
            if (m_goGift != null)
            {
                m_goGift.gameObject.SetActive(giftId > 0 && rewardCount > 0);
                RectTransform itemListRect = m_TableViewVAttr.GetComponent<RectTransform>();
                if (giftId > 0 && rewardCount > 0)
                {
                    itemListRect.sizeDelta = new Vector2(1020, 688);
                }
                else
                {
                    itemListRect.sizeDelta = new Vector2(1020, 918);
                }
            }
            
        }
        
        private void BindBtnLogic(Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            if (action != null)
            {
                btn.onClick.AddListener(action);
            }
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnGiftBuyClick()
        {

        }
    }
}
