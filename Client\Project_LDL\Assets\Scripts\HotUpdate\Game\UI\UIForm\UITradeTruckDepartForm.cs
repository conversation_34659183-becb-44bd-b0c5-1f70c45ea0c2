using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using GameFramework.Event;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTruckDepartForm : UGuiFormEx
    {
        Trade.TradeCargoTransport truckData;
        Animation diceAnim;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            diceAnim = m_goDiceAnim.GetComponent<Animation>();
            AnimationEventHandler handler = m_goDiceAnim.GetComponent<AnimationEventHandler>();
            handler.callback = (index) =>
            {
                if (index == 0)
                {
                    RefreshDice();
                }
            };
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Trade.TradeCargoTransport data)
            {
                ColorLog.Pink("货车车位数据", data);
                truckData = data;
                RefreshPanel();
            }

            PlayDiceAnim();

            m_txtTeam.text = ToolScriptExtend.GetLang(1009) + "1";

            GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckForm, false);

            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);

            GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckForm, true);
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            SetParticleSystemSortingOrder(m_imgCardQuality.gameObject, Depth);
            GameObject coin = m_imgCardQuality.transform.Find("battle_maoyi_jinbi").gameObject;
            SetParticleSystemSortingOrder(coin, Depth + 12);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        void OnItemChange(object sender, GameEventArgs e)
        {
            UpdateRefreshCount();
        }

        void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {
                ColorLog.Pink("队伍变化", teamChangeArgs.TeamType);
                RefreshTeam(teamChangeArgs.TeamType);
            }            
        }

        private void OnBtnRefreshClick()
        {
            if (truckData == null) return;

            // 已使用过免费刷新次数，则检查贸易合约的数量是否足够
            if (truckData.RefreshTimes > 0)
            {
                trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
                if (setting == null) return;
                itemid costID = setting.truck_refeshcost.item_id;
                long costNum = setting.truck_refeshcost.num;
                long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
                if (curNum < costNum)
                {
                    ItemModule itemModule = new(costID);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                    return;
                }
            }

            GameEntry.TradeTruckData.RequestTruckRefresh(truckData.Id, (result) =>
            {
                ColorLog.Pink("刷新货车品质", result);
                if (result != null && result.CargoTransport != null)
                {
                    truckData = result.CargoTransport;

                    PlayDiceAnim();
                    RefreshPanel();
                }
            });
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDescForm, 15);
        }

        private void OnBtnGoClick()
        {
            if (truckData == null || truckData.Van == null) return;

            GameEntry.TradeTruckData.RequestTruckDepart(truckData.Id, Team.TeamType.TradeVanDefend1, (result) =>
            {
                ColorLog.Pink("请求货车出发", result);
                GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckForm);
                Close();
            });
        }

        private void OnBtnSetTeamClick()
        {
            UITeamFormParam param = new()
            {
                Index = 0,
                TeamFormType = UITeamFormType.TradeTruckDefend
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
        }

        void RefreshPanel()
        {
            if (truckData == null || truckData.Van == null) return;

            trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckData.TradeId);
            car_quality quality = truckConfig.car_quality;
            m_imgCardQuality.SetImage(GetBg(quality));
            m_imgTruckQuality.SetImage(GetTruckIcon(quality));
            m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
            Animation animationCard = m_imgCardQuality.GetComponent<Animation>();
            if (animationCard.isPlaying)
            {
                animationCard.Stop();
            }
            animationCard.Play("UITradeTruckDepartForm");
            Animation animation = m_imgQuality.GetComponent<Animation>();
            animation.enabled = quality == car_quality.car_quality_5;
            if (animation.enabled)
            {
                if (animation.isPlaying)
                {
                    animation.Stop();
                }
                animation.Play("UITradeTruckDepartForm_1");
            }
            GameObject effectFrog = m_imgTruckQuality.transform.Find("battle_maoyi_huocheyanwu").gameObject;
            effectFrog.SetActive(false);
            effectFrog.SetActive(true);
            GameObject effect = m_imgQuality.transform.Find("battle_maoyi_pinzhi").gameObject;
            effect.SetActive(false);
            effect.SetActive(quality == car_quality.car_quality_5);
            GameObject effectCoin = m_imgCardQuality.transform.Find("battle_maoyi_jinbi").gameObject;
            effectCoin.SetActive(false);
            effectCoin.SetActive(quality == car_quality.car_quality_5);
            GameObject effectLight = m_imgCardQuality.transform.Find("battle_maoyi_dashaoguang").gameObject;
            effectLight.SetActive(false);
            effectLight.SetActive(quality == car_quality.car_quality_5);

            if (truckData.Boxcar.Count > 0)
            {
                List<Trade.TradeGoods> rewards = new(truckData.Boxcar[0].Goods);
                RefreshReward(rewards);
            }
            UpdateRefreshCount();
            RefreshTeam(Team.TeamType.TradeVanDefend1);

            m_txtTodayCount.text = $"{ToolScriptExtend.GetLang(1121)}{GameEntry.TradeTruckData.TruckTradeTodayCount}/{4}";
            m_txtArriveTime.text = $"{ToolScriptExtend.GetLang(1123)}{TimeHelper.FormatGameTimeWithDays(truckConfig.time)}";
        }

        void RefreshReward(List<Trade.TradeGoods> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            rewards.Sort((a, b) => a.Rob.CompareTo(b.Rob));

            for (int i = 0; i < rewards.Count; i++)
            {
                bool rob = rewards[i].Rob;
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                    uiItemModule.plunder.SetActive(rob);
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.plunder.SetActive(rob);
                    });
                }
            }
        }

        void RefreshTeam(Team.TeamType teamType)
        {
            List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);
            for (int i = 0; i < 5; i++)
            {
                Transform transHeroItem;
                if (i < m_transContentHero.childCount)
                {
                    transHeroItem = m_transContentHero.GetChild(i);
                }
                else
                {
                    transHeroItem = Instantiate(m_transHeroItem, m_transContentHero);
                }
                transHeroItem.gameObject.SetActive(true);
                GameObject empty = transHeroItem.Find("empty").gameObject;
                UIHeroItem item = transHeroItem.Find("UIHeroItem").GetComponent<UIHeroItem>();
                if (teamData != null)
                {
                    Fight.FormationHero heroData = null;
                    foreach (var hero in teamData)
                    {
                        if (hero.Pos == i + 1)
                        {
                            heroData = hero;
                        }
                    }

                    if (heroData != null)
                    {
                        HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                        item.Refresh(heroModule);
                        item.HideTeamBg();
                        item.gameObject.SetActive(true);
                        empty.SetActive(false);
                    }
                    else
                    {
                        item.gameObject.SetActive(false);
                        empty.SetActive(true);
                    }
                }
                else
                {
                    item.gameObject.SetActive(false);
                    empty.SetActive(true);
                }
            }
        }

        void UpdateRefreshCount()
        {
            if (truckData == null) return;
            if (truckData.RefreshTimes > 0)
            {
                trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
                if (setting == null) return;
                itemid costID = setting.truck_refeshcost.item_id;
                long costNum = setting.truck_refeshcost.num;
                long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
                m_txtRefreshCount.text = $"{curNum}/{costNum}";
            }
            else if (truckData.RefreshTimes == 0)
            {
                m_txtRefreshCount.text = ToolScriptExtend.GetLang(1122);
            }
        }

        void PlayDiceAnim()
        {
            if (diceAnim.isPlaying)
            {
                diceAnim.Stop();
            }
            diceAnim.Play("UITradeTruckDepartDice");
            m_imgDice.gameObject.SetActive(false);
        }

        void RefreshDice()
        {
            m_imgDice.gameObject.SetActive(true);

            if (truckData == null || truckData.Van == null) return;

            trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckData.TradeId);
            car_quality quality = truckConfig.car_quality;
            m_imgDice.SetImage(GetDice(quality));
        }

        string GetBg(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_kapian_bg1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_kapian_bg2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_kapian_bg3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_kapian_bg4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_kapian_bg5.png",
                _ => "Sprite/ui_maoyi/maoyi_capian_car1.png",
            };
        }

        string GetTruckIcon(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_capian_car1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_capian_car2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_capian_car3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_capian_car4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_capian_car5.png",
                _ => "Sprite/ui_maoyi/maoyi_capian_car1.png",
            };
        }

        string GetDice(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/touzi1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/touzi1.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/touzi1.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/touzi1.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/touzi5.png",
                _ => "Sprite/ui_maoyi/touzi1.png",
            };
        }
    }
}
