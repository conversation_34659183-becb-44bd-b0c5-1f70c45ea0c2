using System;
using System.Collections.Generic;
using UnityEngine;
using Game.Hotfix.Config;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public class UIHeroItem : MonoBehaviour
    {
        [SerializeField] private UIButton m_btnHero;
        [SerializeField] private UIImage m_imgHeroBg;
        [SerializeField] private UIImage m_imgHeroIcon;
        [SerializeField] private UIImage m_imgHeroPosition;
        [SerializeField] private GameObject m_goTeamBg;
        [SerializeField] private UIText m_txtTeam;
        [SerializeField] private UIImage m_imgServices;
        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIImage m_imgStarBg;
        [SerializeField] private GameObject m_goStar;
        [SerializeField] private List<UIImage> m_listStar;
        [SerializeField] private UIText m_txtChip;
        [SerializeField] private GameObject m_goMaskBg;
        [SerializeField] private GameObject m_goSelected;
        [SerializeField] private GameObject m_goDie;

        public void Refresh(HeroModule heroModule)
        {
            var heroData = GameEntry.LogicData.HeroData;
            m_imgHeroBg.SetImage(GetItemBgPath(heroModule.Quality));
            m_imgHeroIcon.SetImage(heroModule.HeroHead);
            m_imgHeroPosition.SetImage(heroData.GetPositionImgPath(heroModule.Position), false);
            m_imgServices.SetImage(heroData.GetServicesImgPath(heroModule.Services), false);
            m_imgStarBg.SetImage(GetStarBgPath(heroModule.Quality));

            m_txtTeam.text = heroModule.TeamId + "";
            m_goTeamBg.SetActive(heroModule.TeamId > 0);

            var isActive = heroModule.IsActive;
            if (isActive)
            {
                var starNum = heroModule.StarNum;
                var starOrder = heroModule.StarOrder;
                for (int i = 0; i < m_listStar.Count; i++)
                {
                    var starSp = m_listStar[i];
                    string pathStr;
                    if (i < starNum)
                    {
                        pathStr = "Sprite/ui_hero/hero_icon_star5.png";
                    }
                    else if (i < starNum + 1 && starOrder > 0)
                    {
                        pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder);
                    }
                    else
                    {
                        pathStr = "Sprite/ui_hero/hero_icon_star0.png";
                    }

                    starSp.SetImage(pathStr);
                }

                m_txtLevel.text = string.Format("Lv.{0}", heroModule.level);
                m_txtChip.text = "";
            }
            else
            {
                var count = GameEntry.LogicData.BagData.GetAmountById(heroModule.Piece);
                m_txtChip.text = string.Format("{0}/{1}", count, heroModule.Combind);
                m_txtLevel.text = "";
            }

            m_goStar.SetActive(isActive);
            m_goMaskBg.SetActive(!isActive && !heroModule.IsCombind);
        }

        public string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_zhandou/chuzhan_dikuang_hero_1.png",
                quality.quality_purple => "Sprite/ui_zhandou/chuzhan_dikuang_hero_2.png",
                quality.quality_orange => "Sprite/ui_zhandou/chuzhan_dikuang_hero_3.png",
                _ => "Sprite/ui_zhandou/chuzhan_dikuang_hero_1.png",
            };
        }

        public string GetStarBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_zhandou/chuzhan_dikuang_hero_1_1.png",
                quality.quality_purple => "Sprite/ui_zhandou/chuzhan_dikuang_hero_2_1.png",
                quality.quality_orange => "Sprite/ui_zhandou/chuzhan_dikuang_hero_3_1.png",
                _ => "Sprite/ui_zhandou/chuzhan_dikuang_hero_1_1.png",
            };
        }

        public void SetGrey(bool isGrey)
        {
            m_imgHeroBg.SetImageGray(isGrey);
            m_imgHeroIcon.SetImageGray(isGrey);
            m_imgHeroPosition.SetImageGray(isGrey);
            m_imgServices.SetImageGray(isGrey);
            m_imgStarBg.SetImageGray(isGrey);
            for (int i = 0; i < m_listStar.Count; i++)
            {
                m_listStar[i].SetImageGray(isGrey);
            }
        }

        public void SetSelected(bool selected)
        {
            m_goSelected.SetActive(selected);
        }

        public void AddClickListener(UnityAction onClick)
        {
            m_btnHero.onClick.AddListener(onClick);
        }

        public void RemoveClickListener(UnityAction onClick)
        {
            m_btnHero.onClick.RemoveListener(onClick);
        }

        public void RemoveAllClickListeners()
        {
            m_btnHero.onClick.RemoveAllListeners();
        }

        public void SetTeamIndex(int? teamId)
        {
            if (teamId != null)
            {
                m_goTeamBg.SetActive(teamId >= 0);
                m_txtTeam.text = (teamId + 1).ToString();
            }
            else
            {
                m_goTeamBg.SetActive(false);
            }
        }

        public void HideTeamBg()
        {
            m_goTeamBg.SetActive(false);
        }
    }
}
