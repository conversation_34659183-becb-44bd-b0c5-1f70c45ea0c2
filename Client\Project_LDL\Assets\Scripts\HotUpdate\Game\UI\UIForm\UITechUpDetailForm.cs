using System;
using System.Collections;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITechUpDetailForm : UGuiFormEx
    {
        public int techGroup = 0;
        public int curLevel = 0;
        public int nextLevel = 0;
        public bool isMax = false;
        public tech_config nextConfig;
        public tech_config config;
        private List<KeyValuePair<EnumBuildDemand, int>> m_DemandList;
        private List<GameObject> m_DemandObjList = new List<GameObject>();
        private GameObject _scrollViewCost;
        private GameObject _costObj;
        private BuildingModule _curBuildingModule;
        private string m_CheckBoxKey = "techUpgradeCostDiamond";
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
            _scrollViewCost = m_scrollviewCost.transform.Find("Viewport/Content").gameObject;
            _costObj = m_goCostItem.gameObject;
        }

        protected override void OnOpen(object userData)
        {
            var param = userData as TechDetail;
            techGroup = param.techGroup;
            _curBuildingModule = param._curBuildingModule;
            m_DemandList = new();
            if (techGroup > 0)
            {
                curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techGroup);
                config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techGroup, curLevel);
                nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                if(nextConfig != null && config != null)
                {
                    ResetCostParams();
                    OnRefreshPanel();
                    ResetDemandListView();
                    ResetBuildInfo();
                }             
            }
            base.OnOpen(userData);
        }
        public void OnRefreshPanel()
        {
            if (config == null) return;
            m_txtDes.text = ToolScriptExtend.GetLang(config.tech_desc);
            m_txtName.text = ToolScriptExtend.GetLang(config.tech_title);
            m_imgIcon.SetImage(config.tech_icon);
            m_txtCurrent.text = ToolScriptExtend.GetLang(80100009);
            m_txtNext.text = ToolScriptExtend.GetLang(80100010);



            if (config.tech_attributes.Count > 0)
            {
                // m_txtCurrentValue.text = ToolScriptExtend.GetNameByAttrbuteType(config.tech_attributes[0].attributes_type)
                // + ToolScriptExtend.GetAttrLang(config.tech_attributes[0].value);
                float additionValue = (float)config.tech_attributes[0].value / 10000;
                m_txtCurrentValue.text = additionValue * 100 + "%";
            }
            else
            {
                m_txtCurrentValue.text = "0";
            }


            if (config.next_id != 0)
            {
                tech_config nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                float additionValue = (float)nextConfig.tech_attributes[0].value / 10000;
                m_txtNextValue.text = additionValue * 100 + "%";
            }
            else
            {
                //Max
            }

        }

        protected void ResetDemandListView()
        {
            if (m_DemandList.Count > 0)
            {
                for (var i = 0; i < m_DemandList.Count; i++)
                {
                    GameObject itemRect;
                    if (m_DemandObjList.Count < i + 1)
                    {
                        itemRect = Instantiate(this._costObj, _scrollViewCost.transform);
                        itemRect.SetActive(true);
                        m_DemandObjList.Add(itemRect);
                    }
                    else
                    {
                        itemRect = m_DemandObjList[i];
                    }
                    UpdataCostListView(m_DemandList[i], itemRect, m_DemandList[i].Key);
                }
            }
        }

        protected void ResetCostParams()
        {
            if (nextConfig == null)
            {
                return;
            }
            int unLockCount = 0;
            int resNotEnoughCount = 0;
            if (nextConfig.tech_demand != null)
            {

                for (var i = 0; i < nextConfig.tech_demand.Count; i++)
                {
                    var dictionary = new KeyValuePair<EnumBuildDemand, int>(EnumBuildDemand.Build, i);
                    int demandId = nextConfig.tech_demand[i];
                    demand_config config = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    bool isUnlock = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock(
                        config.build_demand.build_type_demand, config.build_demand.build_level_demand,
                        config.build_demand.build_num_demand);
                    if (!isUnlock)
                    {
                        m_DemandList.Insert(0, dictionary);
                        unLockCount += 1;
                    }
                    else
                    {
                        m_DemandList.Add(dictionary);
                    }
                }


                for (var i = 0; i < nextConfig.tech_cost.Count; i++)
                {
                    var cost = nextConfig.tech_cost[i];
                    var dictionary = new KeyValuePair<EnumBuildDemand, int>(EnumBuildDemand.Resource, i);
                    bool isEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(cost.item_id, cost.num);
                    if (!isEnough)
                    {
                        m_DemandList.Insert(resNotEnoughCount + unLockCount, dictionary);
                        resNotEnoughCount += 1;
                    }
                    else
                    {
                        m_DemandList.Add(dictionary);
                    }
                }
            }
            if(nextConfig.pre_id.Count > 0)
            {
                foreach (var techId in nextConfig.pre_id)
                {
                    var dictionary = new KeyValuePair<EnumBuildDemand, int>(EnumBuildDemand.Tech, techId);
                    var config = GameEntry.LogicData.TechData.GetTechConfig(techId);
                    int needLevel = config.tech_lv;
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(config.tech_group);
                    bool isUnlock = curLevel >= needLevel;
                    if (!isUnlock)
                    {
                        m_DemandList.Insert(resNotEnoughCount + unLockCount, dictionary);
                    }
                    else
                    {
                        m_DemandList.Add(dictionary);
                    }
                }
            }
        }

        protected void UpdataCostListView(object userData, GameObject obj, EnumBuildDemand demandType)
        {
            Image imgIcon = obj.transform.Find("imgIcon").GetComponent<Image>();
            Text txtCondition = obj.transform.Find("txtCondition").GetComponent<Text>();
            Button btnBuild = obj.transform.Find("btnBuild").GetComponent<Button>();
            Text txtBuild = obj.transform.Find("btnBuild/txtBuild").GetComponent<Text>();
            GameObject finish = obj.transform.Find("finish").gameObject;
            Button btnTech = obj.transform.Find("btnTech").GetComponent<Button>();
            UIText txtTech = obj.transform.Find("btnTech/txtTech").GetComponent<UIText>();
            string iconPath = String.Empty;
            string strCondition = String.Empty;
            string name = string.Empty;
            string color = "#000000";
            string redColor = "#ff3535";
            itemid itemId = 0;
            btnBuild.gameObject.SetActive(false);
            btnTech.gameObject.SetActive(false);
            txtBuild.text = ToolScriptExtend.GetLang(1100128);
            txtTech.text = ToolScriptExtend.GetLang(1100479);
            var config = (KeyValuePair<EnumBuildDemand, int>)userData;
            switch (demandType)
            {
                case EnumBuildDemand.Build:
                    int configIndex = config.Value;
                    int demandId = nextConfig.tech_demand[configIndex];
                    demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    bool isUnlock = false;
                    BuildingModule buildingModule = null;
                    if (demandConfig.build_demand != null)
                    {
                        buildtype buildTypeDemand = demandConfig.build_demand.build_type_demand;
                        int levelDemand = demandConfig.build_demand.build_level_demand;
                        int buildId = (int)buildTypeDemand * 100 + 1;
                        buildingModule = BuildingModule.Create(buildId, 1, 1);
                        isUnlock = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock(buildTypeDemand, levelDemand, 1);
                        string strColor = isUnlock ? color : redColor;
                        if (buildingModule != null)
                        {
                            name = buildingModule.BuildingName;
                            strCondition = string.Format("<color={0}>Lv.{1}{2}</color>", strColor, levelDemand, name);
                            iconPath = buildingModule.BuildingIcon;
                        }
                    }

                    finish.SetActive(isUnlock);
                    btnBuild.gameObject.SetActive(!isUnlock);
                    btnBuild.onClick.RemoveAllListeners();
                    btnBuild.onClick.AddListener(() =>
                    {
                        if (buildingModule != null)
                        {
                            bool isFind = GameEntry.LogicData.BuildingData.FindBuildingAndOpenMenu(buildingModule.GetBuildingType());
                            if (!isFind)
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm, buildingModule.buildingCfg);
                                Close(true);
                                return;
                            }
                            Close();
                        }
                    });
                    break;
                case EnumBuildDemand.Resource:
                    cost cost = nextConfig.tech_cost[config.Value];
                    itemId = cost.item_id;
                    bool resoureIsEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemId, cost.num);
                    long count = GameEntry.LogicData.BagData.GetAmountById(itemId);
                    string countStr = ToolScriptExtend.FormatNumberWithUnit(count);
                    string costStr = ToolScriptExtend.FormatNumberWithUnit(cost.num);
                    string colorStr = resoureIsEnough ? color : redColor;
                    strCondition = string.Format("<color={0}>{1}</color>/{2}", colorStr, countStr, costStr);
                    finish.SetActive(resoureIsEnough);
                    break;
                case EnumBuildDemand.Tech:
                    int techId = config.Value;
                    var techConfig = GameEntry.LDLTable.GetTableById<tech_config>(techId);
                    int needLevel = techConfig.tech_lv;
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techConfig.tech_group);
                    bool techisUnlock = curLevel >= needLevel;
                    string strColor1 = techisUnlock ? color : redColor;
                    strCondition = string.Format("<color={0}>Lv.{1}  {2}</color>", strColor1, needLevel, ToolScriptExtend.GetLang(techConfig.tech_title));
                    finish.SetActive(techisUnlock);
                    iconPath = techConfig.tech_icon;
                    btnTech.gameObject.SetActive(!techisUnlock);
                    btnTech.onClick.RemoveAllListeners();
                    btnTech.onClick.AddListener(() =>
                    {
                        TechDetail param = new TechDetail();
                        param.techGroup = techConfig.tech_group;
                        param._curBuildingModule = _curBuildingModule;
                        Close();
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITechUpDetailForm, param);
                    });
                    break;
            }

            if (itemId != 0 && demandType != EnumBuildDemand.Tech)
            {
                var _itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemId);
                if (_itemConfig != null)
                {
                    iconPath = _itemConfig.icon;
                }
            }

            if (!string.IsNullOrEmpty(iconPath))
            {
                imgIcon.SetImage(iconPath);
            }

            txtCondition.text = strCondition;
        }

        protected void ResetBuildInfo()
        {
            string dateUtcTimeText = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(nextConfig.time));
            m_txtOldTime.text = dateUtcTimeText;
            int realTime = GameEntry.LogicData.BuildingData.GetRealTechdTime((int)nextConfig.time, false);
            string realTimeTxt = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(realTime));
            m_txtUpLevelTime.text = realTimeTxt;
            m_txtUpLevelTime2.text = realTimeTxt;
            int diamondCostTime = GameEntry.LogicData.BuildingData.GetRealTechdTime((int)nextConfig.time);
            int costDiamond = GameEntry.LogicData.BuildingData.GetCostDiamond(diamondCostTime);
            List<ItemModule> resourceItems;
            bool resIsEnough = GameEntry.LogicData.BuildingData.GetResourceTechIsEnough(nextConfig, out resourceItems);
            //bool itemSpeedEnough = 
            int resTotalDiamond = 0;
            bool timeItemIsEnough = SpeedItemEnough();
            if (!resIsEnough)
            {
                foreach (ItemModule itemModule in resourceItems)
                {
                    int buyRescource = GameEntry.LogicData.BagData.GetDiamondBuyRescource(itemModule.ItemId, itemModule.Count);
                    resTotalDiamond += buyRescource;
                }
            }

            int totalCostDiamond = resTotalDiamond + costDiamond;
            m_txtDoneValue.text = totalCostDiamond.ToString();
            //CanUpGrade()前置科技条件+前置需求条件
            //resIsEnough 资源条件
            //timeItemIsEnough 加速道具条件
            bool canUpGrade = CanUpGrade();

            m_btnDone.gameObject.SetActive(false);
            m_btnDoneItem.gameObject.SetActive(false);

            //立即完成
            if (canUpGrade && resIsEnough && timeItemIsEnough)
            {
                m_btnDoneItem.gameObject.SetActive(true);
                //m_btnDone.SetButtonGray(false);
                m_btnDone.interactable = false;
                ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, true);
                m_btnDoneItem.interactable = true;
                ToolScriptExtend.SetGameObjectGrey(m_btnDoneItem.transform, false);
            }
            else
            {
                m_btnDone.gameObject.SetActive(true);
                //钻石完成
                if (canUpGrade)
                {
                    m_btnDone.interactable = true;
                    ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, false);
                    m_btnDoneItem.interactable = false;
                    ToolScriptExtend.SetGameObjectGrey(m_btnDoneItem.transform, true);
                    m_btnUpLevel.interactable = false;
                    ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, true);

                }//不给钻石完成
                else
                {
                    m_btnDone.interactable = false;
                    ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, true);
                }
                m_btnUpLevel.interactable = false;
                ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, true);
            }

            //普通研究
            if (canUpGrade && resIsEnough)
            {
                m_btnUpLevel.interactable = true;
                ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, false);
            }
            else
            {
                m_btnUpLevel.interactable = false;
                ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, true);
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            _scrollViewCost.DestroyAllChild();
            nextConfig = null;
            m_DemandObjList.Clear();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnDeatialClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITechLevelBuffForm, techGroup);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnDoneItemClick()
        {
            //加速完成道具够
            List<Article.Article> articles = new List<Article.Article>();
            //改
            int buildId = GameEntry.LogicData.TechData.GetCanUseTechBuildId();
            if (buildId <= 0)
            {
                TechDetail param = new TechDetail();
                TechQueue q1 = GameEntry.LogicData.TechData.GetTechQueueLine1();

                param.techGroup = (int)q1.curTechGroup;
                param._curBuildingModule = _curBuildingModule;             
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100470)
                });
                Close();
                return;
            }
            TechQueue useQueue = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)buildId) as TechQueue;

            UseSpeedUpItemParam useSpeedUpItemFormParam = new UseSpeedUpItemParam(_curBuildingModule, useQueue, itemsubtype.itemsubtype_researchspeedup,(itemList) =>
            {
                foreach (ItemModule itemModule in itemList)
                {
                    Article.Article article = new Article.Article();
                    article.Code = (PbGameconfig.itemid)itemModule.ItemId;
                    article.Amount = itemModule.Count;
                    articles.Add(article);
                }
                GameEntry.LogicData.TechData.StudyTech(techGroup, (uint)_curBuildingModule.BuildingId, articles,Tech.CostType.Common, (mesg) =>
                {
                    GameEntry.UI.CloseUIForm(EnumUIForm.UIUseSpeedUpItemForm);
                    GameEntry.UI.CloseUIForm(EnumUIForm.UITechUpDetailForm);
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, new TechDetail()
                    {
                        techGroup = techGroup,
                        _curBuildingModule = _curBuildingModule
                    });
                });
            },nextConfig);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUseSpeedUpItemForm, useSpeedUpItemFormParam);
        }
        //CanUpGrade()前置科技条件+前置需求条件
        //resIsEnough 资源条件
        //timeItemIsEnough 加速道具条件
        private void OnBtnDoneClick()
        {
            List<ItemModule> resourceItems;
            bool timeItemIsEnough = SpeedItemEnough();
            bool resIsEnough = GameEntry.LogicData.BuildingData.GetResourceTechIsEnough(nextConfig, out resourceItems);

            int realBuildingTime = GameEntry.LogicData.BuildingData.GetRealTechdTime((int)nextConfig.time);
            int timeCostDiamond = GameEntry.LogicData.BuildingData.GetCostDiamond(realBuildingTime);

            int resTotalDiamond = 0;
            if (!resIsEnough)
            {
                foreach (ItemModule itemModule in resourceItems)
                {
                    int buyRescource = GameEntry.LogicData.BagData.GetDiamondBuyRescource(itemModule.ItemId, itemModule.Count);
                    resTotalDiamond += buyRescource;
                }
            }
            int totalCostDiamond = resTotalDiamond + timeCostDiamond;
            bool isEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemid.itemid_6, totalCostDiamond);
            if (!isEnough)
            {
                ItemModule itemModule = new ItemModule(itemid.itemid_6);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(totalCostDiamond));
                return;
            }

            Action action = () =>
            {
                List<Article.Article> articles = new List<Article.Article>();
                List<ItemModule> itemlist = new List<ItemModule>();
                itemlist = GetSpeedUpItemList();
                if (itemlist.Count > 0)
                {
                    foreach (ItemModule itemModule in itemlist)
                    {
                        Article.Article article = new Article.Article();
                        article.Code = (PbGameconfig.itemid)itemModule.ItemId;
                        article.Amount = itemModule.Count;
                        articles.Add(article);
                    }
                }
                //资源够 加速道具不够
                if (resIsEnough && !timeItemIsEnough)
                {
                    GameEntry.LogicData.TechData.StudyTech(techGroup, (uint)_curBuildingModule.BuildingId, articles,Tech.CostType.AccelerateItem, (mesg) =>
                    {
                        
                    });
                }//资源不够 加速道具够
                else if (!resIsEnough && timeItemIsEnough)
                {
                    GameEntry.LogicData.TechData.StudyTech(techGroup, (uint)_curBuildingModule.BuildingId, articles,Tech.CostType.ResourcesNot, (mesg) =>
                    {
                        
                    });
                }//资源和加速道具都不够
                else
                {
                    GameEntry.LogicData.TechData.StudyTech(techGroup, (uint)_curBuildingModule.BuildingId, articles,Tech.CostType.ResourcesNot, (mesg) =>
                    {
                        
                    });
                }
            };

            if (totalCostDiamond <= 0)
            {
                action.Invoke();
                return;
            }

            bool isCheck = ToolScriptExtend.GetCommonTipsIsTodayCheckBox(m_CheckBoxKey);
            if (isCheck)
            {
                action.Invoke();
                return;
            }
            //改
            int buildId = GameEntry.LogicData.TechData.GetCanUseTechBuildId();
            if (buildId <= 0)
            {
                TechDetail param = new TechDetail();
                TechQueue q1 = GameEntry.LogicData.TechData.GetTechQueueLine1();

                param.techGroup = (int)q1.curTechGroup;
                param._curBuildingModule = _curBuildingModule;             
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100470)
                });
                Close();
                return;
            }
            CommonTipsParms commonTipsParms = new CommonTipsParms();
            commonTipsParms.desc = ToolScriptExtend.GetLangFormat(1100145, totalCostDiamond.ToString());
            commonTipsParms.okFun = action;
            commonTipsParms.isShowCheck = true;
            commonTipsParms.checkBoxKey = m_CheckBoxKey;
            GameEntry.UI.OpenUIForm(EnumUIForm.UICommonTipForm, commonTipsParms);
        }
        public bool SpeedItemEnough()
        {
            float useTime = 0;
            if (nextConfig == null)
            {
                return false;
            }
            float remainTime = nextConfig.time;
            //int needCount = 0;
            List<ItemModule> speedUpItemList = GetSpeedUpItemList();
            if (speedUpItemList.Count > 0)
            {
                foreach (var itemModule in speedUpItemList)
                {
                    item_config itemConfig = itemModule.GetItemConfig();
                    float time = float.Parse(itemConfig.use_value[0]);
                    for (int i = 0; i < itemModule.Count; i++)
                    {
                        if (useTime < remainTime)
                        {
                            useTime += time;
                            //needCount += 1;
                        }
                        else
                        {
                            return true;
                        }
                             
                    }
                }
            }
            return false;
        }
        protected List<ItemModule> GetSpeedUpItemList()
        {
            List<ItemModule> list = new List<ItemModule>();
            List<itemsubtype> itemTypeList = new List<itemsubtype>();
            itemTypeList.Add(itemsubtype.itemsubtype_researchspeedup);
            itemTypeList.Add(itemsubtype.itemsubtype_generalspeedup);
            foreach (itemsubtype itemSubType in itemTypeList)
            {
                var itemModules = GetBagItemListBySubType(itemSubType);
                foreach (ItemModule itemModule in itemModules)
                {
                    ItemModule _itemModule = new ItemModule();
                    _itemModule.SetData(itemModule.ItemId,GameEntry.LogicData.BagData.GetAmountById(itemModule.ItemId));
                    list.Add(_itemModule);
                }
            }
            return list;
        }
        
        protected List<ItemModule> GetBagItemListBySubType(itemsubtype itemSubType)
        {
            var itemModules = GameEntry.LogicData.BagData.GetDataBySubType(itemSubType);
            itemModules.Sort((a, b) =>
            {
                item_config itemConfigA = a.GetItemConfig();
                item_config itemConfigB = b.GetItemConfig();
                if (itemConfigA.item_subtype == itemConfigB.item_subtype)
                {
                    return float.Parse(itemConfigA.use_value[0]).CompareTo(float.Parse(itemConfigB.use_value[0]));
                }

                return itemConfigA.item_subtype.CompareTo(itemConfigB.item_subtype);
            });
            return itemModules;
        }
        private void OnBtnUpLevelClick()
        {
            //改
            int buildId = GameEntry.LogicData.TechData.GetCanUseTechBuildId();
            if (buildId <= 0)
            {
                TechDetail param = new TechDetail();
                TechQueue q1 = GameEntry.LogicData.TechData.GetTechQueueLine1();

                param.techGroup = (int)q1.curTechGroup;
                param._curBuildingModule = _curBuildingModule;             
                GameEntry.UI.OpenUIForm(EnumUIForm.UITechingForm, param);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100470)
                });
                Close();
                return;
            }

            List<Article.Article> articles = new List<Article.Article>();
            GameEntry.LogicData.TechData.StudyTech(techGroup, (uint)buildId, articles,Tech.CostType.Common, (mesg) =>
            {
                Close();
            });
        }

        public bool CanUpGrade()
        {
            // tech_config nextBuildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, LEVEL + 1);
            bool isBuildDemandUnLock = IsBuildDemandUnLock();
    
            List<cost> buildCost = nextConfig.tech_cost;
            int conditionNum = 0;
            for (int i = 0; i < buildCost.Count; i++)
            {
                cost cost = buildCost[i];
                itemid itemId = cost.item_id;
                long itemNum = cost.num;
                long count = GameEntry.LogicData.BagData.GetAmountById(itemId);
                if (count >= itemNum)
                {
                    conditionNum += 1;
                }
            }

            return isBuildDemandUnLock && conditionNum != 0 && conditionNum >= buildCost.Count;
        }
        public bool IsBuildDemandUnLock()
        {
            foreach (int demandId in nextConfig.tech_demand)
            {
                bool demandUnlock = ToolScriptExtend.GetDemandUnlock(demandId);
                if (demandUnlock == false)
                {
                    return false;
                }
            }
            if(nextConfig.pre_id.Count > 0)
            {
                foreach (var techId in nextConfig.pre_id)
                {
                    var config = GameEntry.LDLTable.GetTableById<tech_config>(techId);
                    int needLevel = config.tech_lv;
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(config.tech_group);
                    if (curLevel < needLevel)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
        // public bool GetResourceIsEnough(out List<ItemModule> rescoureItems)
        // {
        //     rescoureItems = new List<ItemModule>();
        //     int enoughCount = 0;
        //     List<cost> buildCost = nextConfig.tech_cost;
        //     foreach (cost oneCost in buildCost)
        //     {
        //         itemid oneCostItemID = oneCost.item_id;
        //         long oneCostNum = oneCost.num;
        //         long amount = GameEntry.LogicData.BagData.GetAmountById(oneCostItemID);
        //         rescoureItems.Add(new ItemModule(oneCostItemID, oneCostNum));
        //         if (amount >= oneCostNum)
        //         {
        //             enoughCount += 1;
        //         }
        //     }
        //     return enoughCount >= buildCost.Count;
        // }
    }
}
