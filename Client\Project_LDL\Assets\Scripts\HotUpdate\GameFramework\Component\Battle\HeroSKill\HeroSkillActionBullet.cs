using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroSkillActionBullet : HeroSkillActionBase
    {
        private HeroBattleSKill m_HeroBattleSKill;
        private skill_show m_Config;
        private BattleHero m_BattleHero;
        private BattleFiled m_BattleFiled;

        private int m_BulletRemainCnt; //子弹剩余数量
        private float m_BulletInterval;
        private int m_FireSlotIndex = 0;
        private bool m_IsSeq = false; //是否是轮射
        private bool m_IsFireEffectMatch = false; //开火特效是否跟子弹数量一致

        private bool m_CanFinish = false;
        
        private AsyncProcessor m_AsyncProcessor;
        
        public HeroSkillActionBullet(HeroBattleSKill heroBattleSKill, float startTime) : base(heroBattleSKill,
            startTime)
        {
            m_HeroBattleSKill = heroBattleSKill;
            m_Config = heroBattleSKill.ShowConfig;
            m_BattleHero = heroBattleSKill.Caster;
            m_BattleFiled = heroBattleSKill.BattleFiled;

            m_BulletRemainCnt = m_Config.b_count;
            m_BulletInterval = m_Config.b_interval;
            m_FireSlotIndex = 0;
            m_IsSeq = m_Config.b_is_seq;

            m_AsyncProcessor = new AsyncProcessor();
        }

        protected override void OnBegin()
        {
            m_BulletRemainCnt = m_Config.b_count;
            m_BulletInterval = m_Config.b_interval;
            m_FireSlotIndex = 0;
            m_CanFinish = false;
        }
        
        protected override void OnFinish()
        {
        }

        protected override void OnTick(float dt)
        {
            m_AsyncProcessor?.Tick();
        }

        protected override bool CanFinish()
        {
            return m_CanFinish;
        }

        protected override float DoAction()
        {
            m_AsyncProcessor.Process(CreateBulletCo());
            return 0;
        }

        private IEnumerator CreateCopyBulletCo(Transform fireSlots)
        {
            foreach (var target in m_HeroBattleSKill.FinalTargets)
            {
                var list = new List<SkillTargetData> { target };
                m_BattleFiled.BulletCtrl.CreateBullet(m_Config, list, fireSlots.position,
                    fireSlots.rotation);
                if (m_Config.b_copy_interval > 0)
                    yield return CoRoutineUtil.WaitSeconds(m_Config.b_copy_interval);
            }
        }

        private IEnumerator CreateBulletCo()
        {
            while (m_BulletRemainCnt > 0)
            {
                var fireSlots = GetFireSlot(m_IsSeq);
                for (int i = 0; i < fireSlots.Count; i++)
                {
                    PlayFireEffect(fireSlots[i], m_Config.b_muzzle_flash_id);

                    if (m_Config.b_is_copy)
                    {
                        //子弹每个目标一个
                        if (m_HeroBattleSKill.FinalTargets.Count > 1)
                        {
                            m_AsyncProcessor.Process(CreateCopyBulletCo(fireSlots[i]));
                        }
                        else
                        {
                            m_BattleFiled.BulletCtrl.CreateBullet(m_Config, m_HeroBattleSKill.FinalTargets,
                                fireSlots[i].position,
                                fireSlots[i].rotation);
                        }
                    }
                    else
                    {
                        m_BattleFiled.BulletCtrl.CreateBullet(m_Config, m_HeroBattleSKill.FinalTargets,
                            fireSlots[i].position,
                            fireSlots[i].rotation);
                    }
                }

                m_BulletRemainCnt--;
                if (m_BulletRemainCnt > 0)
                {
                    yield return CoRoutineUtil.WaitSeconds(m_BulletInterval +
                                                           Random.Range(m_Config.b_interval_min,
                                                               m_Config.b_interval_max));
                }
            }

            m_CanFinish = true;
        } 

        private List<Transform> GetFireSlot(bool isTurns)
        {
            var fireSlots = m_Config.b_fire_slots;

            List<Transform> slots = new List<Transform>();
            if (isTurns)
            {
                if (m_FireSlotIndex >= fireSlots.Count)
                    m_FireSlotIndex = 0;
                var slot = fireSlots[m_FireSlotIndex];
                var slotTransform = m_BattleHero.GetSlot(slot);
                slots.Add(slotTransform);
                m_FireSlotIndex++;
            }
            else
            {
                for (int i = 0; i < fireSlots.Count; i++)
                {
                    var slotTransform = m_BattleHero.GetSlot(fireSlots[i]);
                    slots.Add(slotTransform);
                }
            }

            return slots;
        }

        private void PlayFireEffect(Transform slot, int effectId)
        {
            if (effectId > 0)
            {
                var effect = m_BattleFiled.EffectCtrl.GetEffect(effectId);
                effect.SetPosition(slot.transform.position);
                effect.SetRotation(slot.transform.rotation);
            }
        }
    }
}