using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingWagonRewardForm : UGuiFormEx
    {
        private int maxTime;
        private int curTime;
        private float deltaTime = 0;
        private int refreshTime = 300;
        private int addTime = 0;
        private bool isHasReward;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            maxTime = GameEntry.LogicData.DungeonData.GetAccumulatedTime();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            OnUpdateInfo();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingWagonForm))
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingWagonForm, 1);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;

                if (addTime >= refreshTime)
                {
                    addTime = 0;
                    OnUpdateInfo();
                }
                else
                {
                    if (curTime < maxTime)
                    {
                        curTime++;
                        var ratio = (float)curTime / maxTime;
                        m_imgProgress.rectTransform.sizeDelta = new Vector2(742 * ratio, 47);
                        m_txtProgress.text = TimeHelper.FormatGameTimeWithDays(curTime);
                    }
                }
                addTime++;
            }
        }

        private void OnUpdateInfo()
        {
            var timeAt = GameEntry.LogicData.DungeonData.AccumulatedRewardAt;
            curTime = (int)(timeAt > 0 ? (long)TimeComponent.Now - timeAt : 0);
            curTime = curTime < maxTime ? curTime : maxTime;
            var ratio = (float)curTime / maxTime;
            m_imgProgress.rectTransform.sizeDelta = new Vector2(742 * ratio, 47);
            m_txtProgress.text = TimeHelper.FormatGameTimeWithDays(curTime);

            var id = GameEntry.LogicData.DungeonData.CurDungeonId;
            var config = GameEntry.LDLTable.GetTableById<dungeon>(id);
            var list = config.place;

            var count = m_transRes.childCount;
            for (int i = 0; i < count; i++)
            {
                var item = m_transRes.GetChild(i);
                var resImg = item.Find("resImg").GetComponent<UIImage>();
                var resTxt = item.Find("resTxt").GetComponent<UIText>();

                var itemInfo = list[i];
                resImg.SetImage(ToolScriptExtend.GetItemIcon(itemInfo.item_id));
                resTxt.text = $"{ToolScriptExtend.FormatNumberWithUnit(itemInfo.num)}/h";
            }

            isHasReward = false;
            var rewardList = new List<reward>();
            var resultTime = Mathf.Floor(curTime / 300) * 300; // 5分钟结算一次
            for (int i = 0; i < list.Count; i++)
            {
                var itemInfo = list[i];
                var itemNum = Mathf.Floor(itemInfo.num * (float)resultTime / 3600);
                if (itemNum > 0)
                {
                    rewardList.Add(new reward
                    {
                        item_id = itemInfo.item_id,
                        num = (long)itemNum,
                    });
                    isHasReward = true;
                }
            }

            m_TableViewH.GetItemCount = () => rewardList.Count;
            m_TableViewH.GetItemGo = () => m_goItem;
            m_TableViewH.UpdateItemCell = (idx, item) =>
            {
                var itemObj = item.transform.Find("itemObj").gameObject;
                var itemModule = itemObj.GetComponent<UIItemModule>();
                var itemInfo = rewardList[idx];
                itemModule.SetData(itemInfo.item_id, itemInfo.num);
                itemModule.DisplayInfo();
                itemModule.SetScale(0.7f);
                itemModule.SetClick(itemModule.OpenTips);
            };
            if (m_TableViewH.itemPrototype) { m_TableViewH.ReloadData(); }
            else { m_TableViewH.InitTableViewByIndex(0); }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnCollectClick()
        {
            if (!isHasReward) return;
            
            GameEntry.LogicData.DungeonData.DungeonAccumulatedRewards(() =>
            {
                OnUpdateInfo();
            });
        }
    }
}
