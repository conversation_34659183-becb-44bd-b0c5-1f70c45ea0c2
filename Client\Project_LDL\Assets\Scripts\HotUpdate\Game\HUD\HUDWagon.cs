using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDWagon : HUDItemTickAble
    {
        [SerializeField] private UIButton m_btnDisplayRoot;
        [SerializeField] private GameObject m_goFight;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goFull;
        [SerializeField] private UIText m_txtTime;
        [SerializeField] private UIText m_txtFull;

        private EL_Building m_Building;
        private BuildingModule m_BuildingModule;
        private long addTime;
        private long maxTime;
        private int rewardTime = 300;
        private int showState;
        private build_menubutton menuCfg;

        void Start()
        {
            m_btnDisplayRoot.onClick.AddListener(OnBtnClick);
        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Building = Owner as EL_Building;
            m_BuildingModule = m_Building?.GetBuildingModule();

            maxTime = GameEntry.LogicData.DungeonData.GetAccumulatedTime();
            m_txtFull.text = TimeHelper.FormatGameTime2((int)maxTime);

            menuCfg = Game.GameEntry.LDLTable.GetTableById<build_menubutton>(1001);

            OnUpdateShow();

            GameEntry.Event.Subscribe(WagonChangeEventArgs.EventId, OnUpdateEvent);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();

            GameEntry.Event.Unsubscribe(WagonChangeEventArgs.EventId, OnUpdateEvent);
        }

        protected override Vector3 GetOffset()
        {
            if (m_BuildingModule != null)
            {
                var offset = m_BuildingModule.GetOffsetCenter();
                return new Vector3(offset.x, 2f, offset.y);
            }
            return base.GetOffset();
        }

        protected override void OnUpdatePerSecond(float dt)
        {
            base.OnUpdatePerSecond(dt);

            if (addTime < maxTime)
            {
                addTime++;

                if (showState != GetShowState(addTime)) { OnUpdateShow(); }
                else { m_txtTime.text = TimeHelper.FormatGameTime2((int)addTime); }
            }
        }

        private void OnUpdateShow()
        {
            var timeAt = GameEntry.LogicData.DungeonData.AccumulatedRewardAt;
            addTime = timeAt > 0 ? (long)TimeComponent.Now - timeAt : 0;
            m_txtTime.text = TimeHelper.FormatGameTime2((int)addTime);

            showState = GetShowState(addTime);
            m_goFight.SetActive(showState == 0);
            m_goReward.SetActive(showState == 1);
            m_goFull.SetActive(showState == 2);
        }

        private int GetShowState(long time)
        {
            if (time < rewardTime) { return 0; }
            else if (time < maxTime) { return 1; }
            else { return 2; }
        }

        private void OnUpdateEvent(object sender, GameEventArgs e)
        {
            OnUpdateShow();
        }

        private void OnBtnClick()
        {
            if (showState == 0)
            {
                if (menuCfg != null)
                {
                    m_Building.OnMenuBtnClick(menuCfg);
                }
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingWagonRewardForm);
            }
        }
    }
}