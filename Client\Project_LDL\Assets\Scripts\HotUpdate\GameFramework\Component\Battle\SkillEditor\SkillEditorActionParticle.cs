#if UNITY_EDITOR
using System;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    public class SkillEditorActionParticle : SkillEditorActionBase
    {
        [PropertyRange(0, "Max")]
        [LabelText("动作开始时间")]
        public float StartTime;
        
        [LabelText("例子特效id")] public int id;
        [LabelText("特效插槽")] public slot slot;

        public SkillEditorActionParticle(SkillEditorPreview skillEditorPreview) : base(skillEditorPreview)
        {
        }

        public override void WriteData(skill_show data)
        {
            base.WriteData(data);
            data.e_start_time = StartTime;
            data.e_effect_id = id;
            data.e_slot = slot;
        }

        public override void ReadData(skill_show data)
        {
            base.ReadData(data);
            StartTime = data.e_start_time;
            id = data.e_effect_id;
            slot = data.e_slot;
        }
    }
}
#endif