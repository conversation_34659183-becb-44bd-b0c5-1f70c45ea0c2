using System;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using Tech;

namespace Game.Hotfix
{
    public class TechData
    {
        public BuildQueueResult techQueueResult;
        public Dictionary<int, Tech.Tech> techDic = new Dictionary<int, Tech.Tech>();
        //                int type        int lineIndex
        public Dictionary<int, Dictionary<int, TechDetailParamsModel>> result = new Dictionary<int, Dictionary<int, TechDetailParamsModel>>();
        public void Init(Roledata.RoleTech tech)
        {
            InitTechShowConfig();
            if (tech == null)
            {
                return;
            }
            foreach (var item in tech.Techs)
            {
                techDic.Add(item.Id, item);
            }
        }
        public void OnTechChange(Tech.Tech tech)
        {
            if (techDic.ContainsKey(tech.Id))
            {
                techDic[tech.Id] = tech;
            }
            else
            {
                techDic.Add(tech.Id, tech);
            }
            GameEntry.Event.Fire(TechChangeEventArgs.EventId, TechChangeEventArgs.Create());
        }
        //单个科技组已完成等级
        public int GetCompeleteTechLevelByGroup(int group)
        {
            if (techDic.TryGetValue(group, out Tech.Tech data))
            {
                return data.Level;
            }
            return 0;
        }

        //单个科技组最大等级
        public int GetTechLevelMax(int group)
        {
            var config = GameEntry.LDLTable.GetTable<tech_config>();
            foreach (var item in config)
            {
                if (item.tech_group == group)
                {
                    return item.tech_lv_limit;
                }
            }
            return 0;
        }
        public bool IsMaxLevel(int group)
        {
            if (techDic.TryGetValue(group, out Tech.Tech data))
            {
                return data.Level >= GetTechLevelMax(group);
            }
            return false;
        }
        //单个类型完成进度
        public int GetLevelByType(int type)
        {
            int total = 0;
            var configs = GameEntry.LDLTable.GetTable<tech_config>();
            foreach (var item in configs)
            {
                if (techDic.TryGetValue(item.tech_group, out Tech.Tech data))
                {
                    if ((Int32)item.tech_type == type)
                    {
                        total += data.Level;
                    }
                }
            }
            return total;
        }
        //单个类型总进度
        public int GetMaxLevelByType(int type)
        {
            int total = 0;
            var configs = GameEntry.LDLTable.GetTable<tech_config>();
            foreach (var item in configs)
            {
                if ((int)item.tech_type == type && item.tech_lv == item.tech_lv_limit)
                {
                    total += item.tech_lv_limit;
                }
            }
            return total;
        }
        public List<tech_config> GetTechConfigByGroup(int group)
        {
            List<tech_config> list = new List<tech_config>();
            if (GameEntry.LDLTable.HaseTable<tech_config>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_config>();
                foreach (var item in data)
                {
                    if (item.tech_group == group && item.tech_lv > 0)
                    {
                        list.Add(item);
                    }
                }
            }
            return list;
        }
        public bool GetTechIslock(List<int> unlock_demand)
        {
            if (unlock_demand.Count > 0)
            {
                foreach (var demandId in unlock_demand)
                {
                    if (!ToolScriptExtend.GetDemandUnlock(demandId))
                    {
                        return true;
                    }
                }
                return false;
            }
            return false;
        }
        public void InitTechShowConfig()
        {
            if (GameEntry.LDLTable.HaseTable<tech_show>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_show>();
                foreach (var item in data)
                {
                    //key
                    int tech_type = item.tech_type;
                    if (!result.ContainsKey(tech_type))
                    {
                        result.Add(tech_type, new Dictionary<int, TechDetailParamsModel>());
                    }
                    string[] arr = item.offset.Split("|");
                    int index = int.Parse(arr[0]);
                    if (!result[tech_type].ContainsKey(index))
                    {
                        result[tech_type][index] = new TechDetailParamsModel();
                    }
                    int offset = int.Parse(arr[1]);
                    switch (offset)
                    {
                        case 1:
                            TechSingleModel model2 = new TechSingleModel(item.tech_id);
                            result[tech_type][index].SetLeftItem(model2);
                            break;
                        case 2:
                            TechSingleModel model3 = new TechSingleModel(item.tech_id);
                            result[tech_type][index].SetMidItem(model3);
                            break;
                        case 3:
                            TechSingleModel model4 = new TechSingleModel(item.tech_id);
                            result[tech_type][index].SetRightItem(model4);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        //通过行获取当前行数据
        public TechDetailParamsModel GetTechShowConfig(int type, int line)
        {
            if (result.ContainsKey(type) && result[type].ContainsKey(line))
            {
                return result[type][line];
            }
            return null;
        }
        public int GetTypeLine(int type)
        {
            if (result.ContainsKey(type))
            {
                return result[type].Count;
            }
            return 0;
        }
        public tech_config GetTechConfig(int techId)
        {
            if (GameEntry.LDLTable.HaseTable<tech_config>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_config>();
                foreach (var item in data)
                {
                    if (item.id == techId)
                    {
                        return item;
                    }
                }
            }
            return null;
        }
        public tech_config GetTechConfigByGroupLevel(int group, int level)
        {
            if (GameEntry.LDLTable.HaseTable<tech_config>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_config>();
                foreach (var item in data)
                {
                    if (item.tech_group == group && item.tech_lv == level)
                    {
                        return item;
                    }
                }
            }
            return null;
        }
        public tech_show GetTechShowByTechId(int techId)
        {
            if (GameEntry.LDLTable.HaseTable<tech_show>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_show>();
                foreach (var item in data)
                {
                    if (item.tech_id == techId)
                    {
                        return item;
                    }
                }
            }
            return null;
        }

        public int GetTechLineByTechId(int techId)
        {
            if (GameEntry.LDLTable.HaseTable<tech_show>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_show>();
                foreach (var item in data)
                {
                    if (item.tech_id == techId)
                    {
                        return int.Parse(item.offset.Split("|")[0]); ;
                    }
                }
            }
            return 0;
        }

        public int GetTechPosByTechId(int techId)
        {
            if (GameEntry.LDLTable.HaseTable<tech_show>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_show>();
                foreach (var item in data)
                {
                    if (item.tech_id == techId)
                    {
                        return int.Parse(item.offset.Split("|")[1]); ;
                    }
                }
            }
            return 0;
        }


        public void StudyTech(int techGroup, uint buildNo, List<Article.Article> articles, CostType costType, Action<TechStudyResp> callback = null)
        {
            TechStudyReq req = new TechStudyReq();
            req.Id = techGroup;
            req.BuildNo = buildNo;
            req.CostType = costType;
            req.Articles.AddRange(articles);
            GameEntry.LDLNet.Send(Protocol.MessageID.TechStudy, req, (message) =>
            {
                var resp = (TechStudyResp)message;
                if (resp != null)
                {
                    techQueueResult = resp.Result;
                    GameEntry.LogicData.BuildingData.HandleBuildQueueResult(techQueueResult);
                    callback?.Invoke(resp);
                    GameEntry.Event.Fire(TechChangeEventArgs.EventId, TechChangeEventArgs.Create());
                }
            });
        }
        public int GetTabIndexByGroup(uint group)
        {
            if (GameEntry.LDLTable.HaseTable<tech_show>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_show>();
                foreach (var item in data)
                {
                    if (item.tech_id == group)
                    {
                        return item.tech_type;
                    }
                }
            }
            return 0;
        }
        public bool IsTechingByTechGroup(int group)
        {
            TechQueue queue = GetTechQueueLine1();
            if (queue != null && queue.curTechGroup == group)
            {
                return true;
            }
            queue = GetTechQueueLine2();
            if (queue != null && queue.curTechGroup == group)
            {
                return true;
            }
            queue = GetTechQueueLine3();
            if (queue != null && queue.curTechGroup == group)
            {
                return true;
            }
            return false;
        }
        public int GetCanUseTechBuildId()
        {
            if (GetTechQueueLine1() == null)
            {
                return 2201;
            }
            if (IsBuyLine2() && GetTechQueueLine2() == null)
            {
                return 2202;
            }
            else if (IsBuyLine3() && GetTechQueueLine3() == null)
            {
                return 2203;
            }
            return 0;
        }
        //返回当前可以用队列，按顺序
        public TechQueue GetTechQueue1()
        {
            TechQueue queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule(2201) as TechQueue;
            if (queueModule == null)
            {
                return queueModule;
            }
            TechQueue queueModule2 = GameEntry.LogicData.QueueData.GetTechQueueModule(2202) as TechQueue;
            if (queueModule2 == null)
            {
                return queueModule;
            }
            TechQueue queueModule3 = GameEntry.LogicData.QueueData.GetTechQueueModule(2203) as TechQueue;
            if (queueModule3 == null)
            {
                return queueModule;
            }
            return queueModule;
        }
        public TechQueue GetTechQueueLine1()
        {
            TechQueue queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule(2201) as TechQueue;
            if (queueModule == null)
            {
                return null;
            }
            return queueModule;
        }
        public TechQueue GetTechQueueLine2()
        {
            TechQueue queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule(2202) as TechQueue;
            if (queueModule == null)
            {
                return null;
            }
            return queueModule;
        }
        public TechQueue GetTechQueueLine3()
        {
            TechQueue queueModule = GameEntry.LogicData.QueueData.GetTechQueueModule(2203) as TechQueue;
            if (queueModule == null)
            {
                return null;
            }
            return queueModule;
        }
        public void CompeleteTech(QueueModule queue)
        {
            GameEntry.LogicData.BuildingData.BuildQueueFinishReq(queue.BindBuildNo, queue.QueueUid, (result) =>
            {
                if (result != null)
                {
                    GameEntry.LogicData.BuildingData.HandleBuildQueueResult(result);
                }

                if (GameEntry.UI.HasUIForm(EnumUIForm.UITechQueueForm))
                {
                    GameEntry.UI.RefreshUIForm(EnumUIForm.UITechQueueForm);
                }
            });
        }
        public void BuyTechLineGift(int line)
        {
            paymentid paymentId;
            if (line == 2)
            {
                paymentId = paymentid.paymentid_20201001;
            }
            else if (line == 3)
            {
                paymentId = paymentid.paymentid_20301002;
            }
            else
            {
                paymentId = paymentid.paymentid_nil;
            }
            if (paymentId != paymentid.paymentid_nil)
            {
                GameEntry.PaymentData.Pay(paymentId);
            }

        }
        public bool IsCanBuyLine2()
        {
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(2202);
            if (buildingModule != null)
            {
                return true;
            }
            return false;
        }
        public bool IsBuyLine2()
        {
            bool isPast = GameEntry.LogicData.PrivilegeData.GetPrivilegeIsPast((PbGameconfig.privilege_type)privilege_type.build_tech_2);
            return !isPast;
        }
        public bool IsCanBuyLine3()
        {
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(2203);
            if (buildingModule == null)
            {
                return false;
            }
            if (IsBuyLine2())
            {
                return true;
            }
            return false;
        }
        public bool IsBuyLine3()
        {
            bool isPast = GameEntry.LogicData.PrivilegeData.GetPrivilegeIsPast((PbGameconfig.privilege_type)privilege_type.build_tech_3);
            return !isPast;
        }

        //返回经济推荐组
        public tech_config GetSortEconomicsConfig()
        {
            if (GameEntry.LDLTable.HaseTable<tech_config>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_config>();
                //根据sort_economics排序
                data.Sort((a, b) => a.sort_economics.CompareTo(b.sort_economics));

                foreach (var config in data)
                {
                    if (config.sort_economics > 0)
                    {
                        int curLevel = GetCompeleteTechLevelByGroup(config.tech_group);
                        if (curLevel >= config.tech_lv_limit)
                        {
                            continue;
                        }
                        return config;
                        //如果满足升级条件
                        // if (CanUpGrade(config))
                        // {
                        //     return config;
                        // }
                    }
                }
            }
            return null;
        }
        //返回军事推荐组
        public tech_config GetSortMilitaryConfig()
        {
            if (GameEntry.LDLTable.HaseTable<tech_config>())
            {
                var data = GameEntry.LDLTable.GetTable<tech_config>();
                //根据sort_combat排序
                data.Sort((a, b) => a.sort_military.CompareTo(b.sort_military));

                foreach (var config in data)
                {
                    if (config.sort_military > 0)
                    {
                        int curLevel = GetCompeleteTechLevelByGroup(config.tech_group);
                        if (curLevel >= config.tech_lv_limit)
                        {
                            continue;
                        }
                        return config;
                        //如果满足升级条件
                        // if (CanUpGrade(config))
                        // {
                        //     return config;
                        // }
                    }
                }
            }
            return null;
        }

        public bool CanUpGrade(tech_config data)
        {
            // tech_config nextBuildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(buildingCfg.build_type, LEVEL + 1);
            if (data == null || data.tech_demand.Count <= 0)
            {
                return true;
            }
            foreach (int demandId in data.tech_demand)
            {
                bool demandUnlock = ToolScriptExtend.GetDemandUnlock(demandId);
                if (demandUnlock == false)
                {
                    return false;
                }
            }
            if(data.pre_id.Count > 0)
            {
                foreach (var techId in data.pre_id)
                {
                    var config = GameEntry.LDLTable.GetTableById<tech_config>(techId);
                    int needLevel = config.tech_lv;
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(config.tech_group);
                    if (curLevel < needLevel)
                    {
                        return false;
                    }
                }
            }
            // for (int i = 0; i < data.tech_cost.Count; i++)
            // {
            //     cost cost = data.tech_cost[i];
            //     itemid itemId = cost.item_id;
            //     long itemNum = cost.num;
            //     long count = GameEntry.LogicData.BagData.GetAmountById(itemId);
            //     if (count < itemNum)
            //     {
            //         return false;
            //     }
            // }
            return true;
        }
        public bool CanUpGradePreTech(tech_config data)
        {
            if (data.pre_id.Count > 0)
            {
                foreach (var techId in data.pre_id)
                {
                    var config = GameEntry.LDLTable.GetTableById<tech_config>(techId);
                    int needLevel = config.tech_lv;
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(config.tech_group);
                    if (curLevel < needLevel)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
        public bool isTechDemandUnLock(int techId)
        {
            tech_config config = GameEntry.LDLTable.GetTableById<tech_config>(techId);
            if (config == null)
            {
                return false;
            }
            int group = config.tech_group;
            int curLevel = GetCompeleteTechLevelByGroup(group);
            if (curLevel >= config.tech_lv)
            {
                return true;
            }
            return false;
        }

        public int GetTechLineCount()
        {
            int count = 1;
            if (IsBuyLine2())
            {
                count += 1;
            }
            if (IsBuyLine3())
            {
                count += 1;
            }
            return count;
        }
        public int GetTechingCount()
        {
            int count = GetTechLineCount();
            if (GetTechQueueLine1() == null)
            {
                count -= 1;
            }
            if (GetTechQueueLine2() == null && IsBuyLine2())
            {
                count -= 1;
            }
            if (GetTechQueueLine3() == null && IsBuyLine3())
            {
                count -= 1;
            }
            return count;
        }
    }
}
 
